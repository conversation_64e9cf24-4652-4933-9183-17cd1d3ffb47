<Window x:Class="DriverManagementSystem.Views.DatabaseConnectionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="🔗 إعداد اتصال قاعدة البيانات - SQL Server" 
        Height="700" Width="900"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        Background="#F8F9FA"
        FontFamily="Segoe UI"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#007ACC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="6"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#005A9E"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#004578"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="BorderBrush" Value="#E1E5E9"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#007ACC"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" CornerRadius="8,8,0,0" Padding="20,15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="🔗" FontSize="24" Margin="0,0,10,0"/>
                <StackPanel>
                    <TextBlock Text="إعداد اتصال قاعدة البيانات" 
                             FontSize="20" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="تكوين الاتصال مع SQL Server وفحص الجداول" 
                             FontSize="14" Foreground="#E8F4FD" Margin="0,5,0,0"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Main Content -->
        <Border Grid.Row="1" Background="White" CornerRadius="0,0,8,8" 
                BorderThickness="1" BorderBrush="#E1E5E9">
            <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
                <StackPanel>
                    
                    <!-- Connection Settings -->
                    <GroupBox Header="⚙️ إعدادات الاتصال" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,20">
                        <Grid Margin="15">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Server Name -->
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم الخادم:" 
                                     VerticalAlignment="Center" FontWeight="Medium" Margin="0,0,10,0"/>
                            <TextBox Grid.Row="0" Grid.Column="1" x:Name="ServerNameTextBox" 
                                   Text="localhost" Style="{StaticResource ModernTextBoxStyle}" Margin="0,5"/>

                            <!-- Database Name -->
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="اسم قاعدة البيانات:" 
                                     VerticalAlignment="Center" FontWeight="Medium" Margin="0,0,10,0"/>
                            <TextBox Grid.Row="1" Grid.Column="1" x:Name="DatabaseNameTextBox" 
                                   Text="SFDSYS" Style="{StaticResource ModernTextBoxStyle}" Margin="0,5"/>

                            <!-- Authentication Type -->
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="نوع المصادقة:" 
                                     VerticalAlignment="Center" FontWeight="Medium" Margin="0,0,10,0"/>
                            <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Margin="0,5">
                                <RadioButton x:Name="WindowsAuthRadio" Content="Windows Authentication" 
                                           IsChecked="True" Margin="0,0,20,0" FontSize="14"/>
                                <RadioButton x:Name="SqlAuthRadio" Content="SQL Server Authentication" 
                                           FontSize="14"/>
                            </StackPanel>

                            <!-- Username -->
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="اسم المستخدم:" 
                                     VerticalAlignment="Center" FontWeight="Medium" Margin="0,0,10,0"
                                     x:Name="UsernameLabel"/>
                            <TextBox Grid.Row="3" Grid.Column="1" x:Name="UsernameTextBox" 
                                   Style="{StaticResource ModernTextBoxStyle}" Margin="0,5"
                                   IsEnabled="False"/>

                            <!-- Password -->
                            <TextBlock Grid.Row="4" Grid.Column="0" Text="كلمة المرور:" 
                                     VerticalAlignment="Center" FontWeight="Medium" Margin="0,0,10,0"
                                     x:Name="PasswordLabel"/>
                            <PasswordBox Grid.Row="4" Grid.Column="1" x:Name="PasswordBox" 
                                       Margin="0,5" IsEnabled="False" Padding="12,8" FontSize="14"
                                       BorderThickness="2" BorderBrush="#E1E5E9"/>

                            <!-- Test Connection Button -->
                            <Button Grid.Row="5" Grid.Column="1" x:Name="TestConnectionButton" 
                                  Content="🔍 اختبار الاتصال" 
                                  Style="{StaticResource ModernButtonStyle}"
                                  Background="#28A745" Margin="0,15,0,5"
                                  Click="TestConnectionButton_Click"/>
                        </Grid>
                    </GroupBox>

                    <!-- Connection Status -->
                    <GroupBox Header="📊 حالة الاتصال" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,20">
                        <StackPanel Margin="15">
                            <TextBlock x:Name="ConnectionStatusText" 
                                     Text="⏳ لم يتم اختبار الاتصال بعد" 
                                     FontSize="14" Margin="0,0,0,10"/>
                            <ProgressBar x:Name="ConnectionProgressBar" 
                                       Height="8" IsIndeterminate="False" 
                                       Visibility="Collapsed" Margin="0,0,0,10"/>
                        </StackPanel>
                    </GroupBox>

                    <!-- Database Tables -->
                    <GroupBox Header="📋 جداول قاعدة البيانات" FontSize="16" FontWeight="SemiBold">
                        <Grid Margin="15">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                                <Button x:Name="RefreshTablesButton" Content="🔄 تحديث القائمة" 
                                      Style="{StaticResource ModernButtonStyle}"
                                      Background="#17A2B8" Margin="0,0,10,0"
                                      Click="RefreshTablesButton_Click"/>
                                <TextBlock x:Name="TablesCountText" Text="عدد الجداول: 0" 
                                         VerticalAlignment="Center" FontWeight="Medium"/>
                            </StackPanel>
                            
                            <DataGrid Grid.Row="1" x:Name="TablesDataGrid" 
                                    Height="200" AutoGenerateColumns="False"
                                    IsReadOnly="True" GridLinesVisibility="Horizontal"
                                    HeadersVisibility="Column" CanUserReorderColumns="False"
                                    Background="White" BorderBrush="#E1E5E9" BorderThickness="1">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="اسم الجدول" Binding="{Binding TableName}" Width="*"/>
                                    <DataGridTextColumn Header="عدد الصفوف" Binding="{Binding RowCount}" Width="100"/>
                                    <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </GroupBox>

                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- Footer Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                  HorizontalAlignment="Left" Margin="0,15,0,0">
            <Button x:Name="SaveButton" Content="💾 حفظ الإعدادات" 
                  Style="{StaticResource ModernButtonStyle}"
                  Background="#28A745" Margin="0,0,10,0"
                  Click="SaveButton_Click"/>
            <Button x:Name="CreateDatabaseButton" Content="🏗️ إنشاء قاعدة البيانات" 
                  Style="{StaticResource ModernButtonStyle}"
                  Background="#FD7E14" Margin="0,0,10,0"
                  Click="CreateDatabaseButton_Click"/>
            <Button x:Name="CancelButton" Content="❌ إلغاء" 
                  Style="{StaticResource ModernButtonStyle}"
                  Background="#6C757D"
                  Click="CancelButton_Click"/>
        </StackPanel>

    </Grid>
</Window>
