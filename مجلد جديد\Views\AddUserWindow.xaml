<Window x:Class="DriverManagementSystem.Views.AddUserWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة مستخدم جديد" 
        Height="500" Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F8F9FA"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="➕" FontSize="20" Foreground="White" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBlock Text="إضافة مستخدم جديد" 
                         FontSize="18" 
                         FontWeight="Bold" 
                         Foreground="White"
                         VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Form -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="30">
            <StackPanel>
                <!-- Full Name -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="الاسم الكامل *" FontWeight="Medium" Margin="0,0,0,5"/>
                    <TextBox x:Name="FullNameTextBox" 
                             Height="35" 
                             Padding="10,8" 
                             BorderBrush="#CCC" 
                             BorderThickness="1"/>
                </StackPanel>

                <!-- Username -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="اسم المستخدم *" FontWeight="Medium" Margin="0,0,0,5"/>
                    <TextBox x:Name="UsernameTextBox" 
                             Height="35" 
                             Padding="10,8" 
                             BorderBrush="#CCC" 
                             BorderThickness="1"/>
                </StackPanel>

                <!-- Email -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="البريد الإلكتروني *" FontWeight="Medium" Margin="0,0,0,5"/>
                    <TextBox x:Name="EmailTextBox" 
                             Height="35" 
                             Padding="10,8" 
                             BorderBrush="#CCC" 
                             BorderThickness="1"/>
                </StackPanel>

                <!-- Password -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="كلمة المرور *" FontWeight="Medium" Margin="0,0,0,5"/>
                    <PasswordBox x:Name="PasswordBox" 
                                 Height="35" 
                                 Padding="10,8" 
                                 BorderBrush="#CCC" 
                                 BorderThickness="1"/>
                </StackPanel>

                <!-- Role -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="الدور *" FontWeight="Medium" Margin="0,0,0,5"/>
                    <ComboBox x:Name="RoleComboBox" 
                              Height="35" 
                              Padding="10,8" 
                              BorderBrush="#CCC" 
                              BorderThickness="1">
                        <ComboBoxItem Content="مسئول النظام" Tag="Admin"/>
                        <ComboBoxItem Content="مشرف" Tag="Manager"/>
                        <ComboBoxItem Content="مستخدم" Tag="User" IsSelected="True"/>
                        <ComboBoxItem Content="مشاهد" Tag="Viewer"/>
                    </ComboBox>
                </StackPanel>

                <!-- Active Status -->
                <CheckBox x:Name="IsActiveCheckBox" 
                          Content="المستخدم نشط" 
                          IsChecked="True" 
                          Margin="0,10,0,0"/>

                <!-- Notes -->
                <StackPanel Margin="0,15,0,0">
                    <TextBlock Text="ملاحظات" FontWeight="Medium" Margin="0,0,0,5"/>
                    <TextBox x:Name="NotesTextBox" 
                             Height="60" 
                             Padding="10,8" 
                             BorderBrush="#CCC" 
                             BorderThickness="1"
                             TextWrapping="Wrap"
                             AcceptsReturn="True"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" Padding="30,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                <Button x:Name="SaveButton" 
                        Content="💾 حفظ" 
                        Background="{StaticResource PrimaryBrush}" 
                        Foreground="White"
                        Padding="20,8" 
                        Margin="0,0,15,0"
                        BorderThickness="0" 
                        Cursor="Hand"
                        Click="SaveButton_Click"/>
                <Button x:Name="CancelButton" 
                        Content="❌ إلغاء" 
                        Background="#6C757D" 
                        Foreground="White"
                        Padding="20,8" 
                        BorderThickness="0" 
                        Cursor="Hand"
                        Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
