using System;
using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.ViewModels;

namespace DriverManagementSystem.Views
{
    public partial class VehicleManagementWindow : Window
    {
        public VehicleManagementWindow()
        {
            InitializeComponent();
            DataContext = new VehicleManagementViewModel();
        }

        private void CloseWindow_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// فتح نافذة إضافة سائق جديد محسنة
        /// </summary>
        private void AddNewDriverButton_Click(object sender, RoutedEventArgs e)
        {
            // تم حذف نافذة إضافة السائق - يمكن إضافة نافذة بديلة لاحقاً
            MessageBox.Show("هذه الميزة غير متاحة حالياً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// إدراج البيانات الأولية للسائقين
        /// </summary>
        private async void ImportInitialDataButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "هل أنت متأكد من إدراج البيانات الأولية للسائقين؟\n\n" +
                    "سيتم إضافة 31 سائق مع جميع بياناتهم الكاملة.\n" +
                    "هذه العملية لن تؤثر على البيانات الموجودة مسبقاً.",
                    "تأكيد إدراج البيانات",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // إظهار مؤشر التحميل
                    var loadingWindow = new Window
                    {
                        Title = "جاري إدراج البيانات...",
                        Width = 300,
                        Height = 150,
                        WindowStartupLocation = WindowStartupLocation.CenterOwner,
                        Owner = this,
                        ResizeMode = ResizeMode.NoResize,
                        Content = new StackPanel
                        {
                            Margin = new Thickness(20),
                            Children =
                            {
                                new TextBlock
                                {
                                    Text = "🔄 جاري إدراج بيانات السائقين...",
                                    FontSize = 14,
                                    HorizontalAlignment = HorizontalAlignment.Center,
                                    Margin = new Thickness(0, 20, 0, 20)
                                },
                                new ProgressBar
                                {
                                    IsIndeterminate = true,
                                    Height = 20
                                }
                            }
                        }
                    };

                    loadingWindow.Show();

                    try
                    {
                        // إدراج البيانات
                        using var context = new Data.ApplicationDbContext();
                        await Data.SeedDriversData.SeedDriversAsync(context);

                        loadingWindow.Close();

                        // إعادة تحميل البيانات
                        if (DataContext is VehicleManagementViewModel viewModel)
                        {
                            viewModel.LoadData();
                        }

                        MessageBox.Show(
                            "✅ تم إدراج بيانات السائقين بنجاح!\n\n" +
                            "تم إضافة 31 سائق مع جميع بياناتهم الكاملة.\n" +
                            "يمكنك الآن رؤية البيانات في الجدول.",
                            "نجح الإدراج",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        loadingWindow.Close();

                        if (ex.Message.Contains("يوجد") && ex.Message.Contains("سائق في قاعدة البيانات"))
                        {
                            MessageBox.Show(
                                "ℹ️ البيانات موجودة بالفعل!\n\n" +
                                "يوجد سائقين في قاعدة البيانات مسبقاً.\n" +
                                "لن يتم إضافة بيانات مكررة.",
                                "البيانات موجودة",
                                MessageBoxButton.OK,
                                MessageBoxImage.Information);
                        }
                        else
                        {
                            MessageBox.Show($"❌ خطأ في إدراج البيانات: {ex.Message}", "خطأ",
                                          MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عملية الإدراج: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
