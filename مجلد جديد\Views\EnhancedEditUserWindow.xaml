<Window x:Class="DriverManagementSystem.Views.EnhancedEditUserWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تعديل المستخدم - نظام متطور" 
        Height="650" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F5F7FA"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="45"/>
            <Setter Property="Padding" Value="15,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                        Padding="{TemplateBinding Padding}"
                                        VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#2196F3"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernPasswordBoxStyle" TargetType="PasswordBox">
            <Setter Property="Height" Value="45"/>
            <Setter Property="Padding" Value="15,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="PasswordBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                        Padding="{TemplateBinding Padding}"
                                        VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#2196F3"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Height" Value="45"/>
            <Setter Property="Padding" Value="15,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="50"/>
            <Setter Property="Padding" Value="25,12"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="10"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.9"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="Linear Gradient(45deg, #667eea 0%, #764ba2 100%)" Padding="30,25">
            <StackPanel>
                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="✏️" FontSize="24" Foreground="White" Margin="0,0,15,0" VerticalAlignment="Center"/>
                    <StackPanel>
                        <TextBlock x:Name="HeaderTitle" Text="تعديل المستخدم" 
                                 FontSize="22" 
                                 FontWeight="Bold" 
                                 Foreground="White"/>
                        <TextBlock x:Name="HeaderSubtitle" Text="تعديل بيانات المستخدم المحدد" 
                                 FontSize="14" 
                                 Foreground="White" 
                                 Opacity="0.9"/>
                    </StackPanel>
                </StackPanel>
                
                <!-- User Info Summary -->
                <Border Background="rgba(255,255,255,0.2)" CornerRadius="8" Padding="15" Margin="0,10,0,0">
                    <StackPanel Orientation="Horizontal">
                        <Border x:Name="UserAvatarHeader" Width="40" Height="40" CornerRadius="20" Background="#4CAF50" Margin="0,0,15,0">
                            <TextBlock x:Name="UserIconHeader" Text="👤" FontSize="18" Foreground="White" 
                                     HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <StackPanel>
                            <TextBlock x:Name="UserNameHeader" Text="اسم المستخدم" FontSize="14" FontWeight="Bold" Foreground="White"/>
                            <TextBlock x:Name="UserDetailsHeader" Text="معلومات المستخدم" FontSize="12" Foreground="White" Opacity="0.8"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Border>

        <!-- Form -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="30,25">
            <StackPanel>
                <!-- Personal Information Section -->
                <Border Background="White" CornerRadius="12" Padding="25" Margin="0,0,0,20">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.1" BlurRadius="10"/>
                    </Border.Effect>
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                            <TextBlock Text="👤" FontSize="18" Margin="0,0,10,0" VerticalAlignment="Center"/>
                            <TextBlock Text="المعلومات الشخصية" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- Full Name -->
                        <StackPanel Margin="0,0,0,20">
                            <TextBlock Text="الاسم الكامل *" FontWeight="Medium" FontSize="14" Margin="0,0,0,8" Foreground="#333"/>
                            <TextBox x:Name="FullNameTextBox" 
                                     Style="{StaticResource ModernTextBoxStyle}"
                                     TextChanged="ValidateForm"/>
                        </StackPanel>

                        <!-- Username -->
                        <StackPanel Margin="0,0,0,20">
                            <TextBlock Text="اسم المستخدم *" FontWeight="Medium" FontSize="14" Margin="0,0,0,8" Foreground="#333"/>
                            <TextBox x:Name="UsernameTextBox" 
                                     Style="{StaticResource ModernTextBoxStyle}"
                                     TextChanged="ValidateForm"/>
                            <TextBlock x:Name="UsernameNote" Text="ملاحظة: تغيير اسم المستخدم قد يؤثر على تسجيل الدخول" 
                                     FontSize="11" Foreground="#FF9800" Margin="5,5,0,0"/>
                        </StackPanel>

                        <!-- Email -->
                        <StackPanel Margin="0,0,0,0">
                            <TextBlock Text="البريد الإلكتروني *" FontWeight="Medium" FontSize="14" Margin="0,0,0,8" Foreground="#333"/>
                            <TextBox x:Name="EmailTextBox" 
                                     Style="{StaticResource ModernTextBoxStyle}"
                                     TextChanged="ValidateForm"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Security Information Section -->
                <Border Background="White" CornerRadius="12" Padding="25" Margin="0,0,0,20">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.1" BlurRadius="10"/>
                    </Border.Effect>
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                            <TextBlock Text="🔒" FontSize="18" Margin="0,0,10,0" VerticalAlignment="Center"/>
                            <TextBlock Text="تغيير كلمة المرور (اختياري)" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>

                        <Border Background="#FFF3E0" CornerRadius="8" Padding="15" Margin="0,0,0,20">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="ℹ️" FontSize="14" Margin="0,0,10,0" VerticalAlignment="Center"/>
                                <TextBlock Text="اتركها فارغة إذا كنت لا تريد تغيير كلمة المرور الحالية" 
                                         FontSize="12" Foreground="#E65100" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>

                        <!-- New Password -->
                        <StackPanel Margin="0,0,0,20">
                            <TextBlock Text="كلمة المرور الجديدة" FontWeight="Medium" FontSize="14" Margin="0,0,0,8" Foreground="#333"/>
                            <PasswordBox x:Name="PasswordBox" 
                                         Style="{StaticResource ModernPasswordBoxStyle}"
                                         PasswordChanged="ValidateForm"/>
                        </StackPanel>

                        <!-- Confirm New Password -->
                        <StackPanel Margin="0,0,0,0">
                            <TextBlock Text="تأكيد كلمة المرور الجديدة" FontWeight="Medium" FontSize="14" Margin="0,0,0,8" Foreground="#333"/>
                            <PasswordBox x:Name="ConfirmPasswordBox" 
                                         Style="{StaticResource ModernPasswordBoxStyle}"
                                         PasswordChanged="ValidateForm"/>
                            <TextBlock x:Name="PasswordError" Text="كلمة المرور غير متطابقة" 
                                     FontSize="12" Foreground="#F44336" Margin="5,5,0,0" Visibility="Collapsed"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Role and Settings Section -->
                <Border Background="White" CornerRadius="12" Padding="25" Margin="0,0,0,20">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.1" BlurRadius="10"/>
                    </Border.Effect>
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                            <TextBlock Text="⚙️" FontSize="18" Margin="0,0,10,0" VerticalAlignment="Center"/>
                            <TextBlock Text="الدور والإعدادات" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- Role -->
                        <StackPanel Margin="0,0,0,20">
                            <TextBlock Text="دور المستخدم *" FontWeight="Medium" FontSize="14" Margin="0,0,0,8" Foreground="#333"/>
                            <ComboBox x:Name="RoleComboBox" 
                                      Style="{StaticResource ModernComboBoxStyle}"
                                      SelectionChanged="RoleComboBox_SelectionChanged">
                                <ComboBoxItem Content="👨‍💼 مسئول النظام" Tag="Admin"/>
                                <ComboBoxItem Content="👨‍💻 مشرف" Tag="Manager"/>
                                <ComboBoxItem Content="👤 مستخدم" Tag="User"/>
                                <ComboBoxItem Content="👁️ مشاهد" Tag="Viewer"/>
                            </ComboBox>
                            <TextBlock x:Name="RoleWarning" Text="⚠️ تغيير الدور سيؤثر على صلاحيات المستخدم" 
                                     FontSize="12" Foreground="#FF9800" Margin="5,8,0,0" Visibility="Collapsed"/>
                        </StackPanel>

                        <!-- Active Status -->
                        <Border Background="#F8F9FA" CornerRadius="8" Padding="15" Margin="0,0,0,20">
                            <StackPanel Orientation="Horizontal">
                                <CheckBox x:Name="IsActiveCheckBox" 
                                          VerticalAlignment="Center"
                                          Margin="0,0,10,0"
                                          Checked="ActiveStatus_Changed"
                                          Unchecked="ActiveStatus_Changed"/>
                                <StackPanel>
                                    <TextBlock Text="حالة المستخدم" FontWeight="Medium" FontSize="14"/>
                                    <TextBlock x:Name="ActiveStatusDescription" Text="المستخدم نشط ويمكنه تسجيل الدخول" FontSize="12" Foreground="#666"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>

                        <!-- Notes -->
                        <StackPanel>
                            <TextBlock Text="ملاحظات" FontWeight="Medium" FontSize="14" Margin="0,0,0,8" Foreground="#333"/>
                            <TextBox x:Name="NotesTextBox" 
                                     Height="80" 
                                     Padding="15,12" 
                                     FontSize="14"
                                     BorderBrush="#E0E0E0"
                                     BorderThickness="2"
                                     Background="White"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"
                                     VerticalScrollBarVisibility="Auto">
                                <TextBox.Style>
                                    <Style TargetType="TextBox">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="TextBox">
                                                    <Border Background="{TemplateBinding Background}"
                                                            BorderBrush="{TemplateBinding BorderBrush}"
                                                            BorderThickness="{TemplateBinding BorderThickness}"
                                                            CornerRadius="8">
                                                        <ScrollViewer x:Name="PART_ContentHost" 
                                                                    Padding="{TemplateBinding Padding}"/>
                                                    </Border>
                                                    <ControlTemplate.Triggers>
                                                        <Trigger Property="IsFocused" Value="True">
                                                            <Setter Property="BorderBrush" Value="#2196F3"/>
                                                        </Trigger>
                                                    </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </TextBox.Style>
                            </TextBox>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- User Statistics -->
                <Border Background="White" CornerRadius="12" Padding="25" Margin="0,0,0,0">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.1" BlurRadius="10"/>
                    </Border.Effect>
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <TextBlock Text="📊" FontSize="18" Margin="0,0,10,0" VerticalAlignment="Center"/>
                            <TextBlock Text="إحصائيات المستخدم" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="تاريخ الإنشاء" FontSize="12" Foreground="#666" Margin="0,0,0,5"/>
                                <TextBlock x:Name="CreatedDateText" Text="2024/01/01" FontSize="14" FontWeight="Medium"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                <TextBlock Text="آخر دخول" FontSize="12" Foreground="#666" Margin="0,0,0,5"/>
                                <TextBlock x:Name="LastLoginText" Text="لم يسجل دخول" FontSize="14" FontWeight="Medium"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" Padding="30,20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                <Button x:Name="SaveButton" 
                        Content="💾 حفظ التغييرات" 
                        Background="#4CAF50" 
                        Foreground="White"
                        Style="{StaticResource ModernButtonStyle}"
                        Margin="0,0,15,0"
                        Click="SaveButton_Click"/>
                <Button x:Name="ResetPasswordButton" 
                        Content="🔄 إعادة تعيين كلمة المرور" 
                        Background="#FF9800" 
                        Foreground="White"
                        Style="{StaticResource ModernButtonStyle}"
                        Margin="0,0,15,0"
                        Click="ResetPasswordButton_Click"/>
                <Button x:Name="CancelButton" 
                        Content="❌ إلغاء" 
                        Background="#757575" 
                        Foreground="White"
                        Style="{StaticResource ModernButtonStyle}"
                        Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
