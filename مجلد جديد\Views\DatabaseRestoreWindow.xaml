<Window x:Class="DriverManagementSystem.Views.DatabaseRestoreWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="استعادة قاعدة البيانات - نظام إدارة الزيارات الميدانية"
        Height="600"
        Width="800"
        WindowStartupLocation="CenterScreen"
        Background="#F5F7FA"
        FlowDirection="RightToLeft"
        FontFamily="Arial"
>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="20,15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="🗄️" FontSize="24" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBlock Text="استعادة قاعدة البيانات"
                         FontSize="24" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <StackPanel>
                <!-- معلومات قاعدة البيانات -->
                <Border Background="White" BorderBrush="#E1E8ED" BorderThickness="1"
                        CornerRadius="8" Padding="20" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="📊 معلومات قاعدة البيانات الحالية" FontSize="18" FontWeight="Bold"
                                 Foreground="#2C3E50" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="📋 الزيارات الميدانية:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="VisitsCountText" Text="جاري التحميل..." FontSize="14" Foreground="#666" Margin="0,0,0,10"/>
                                
                                <TextBlock Text="🚗 السائقين:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="DriversCountText" Text="جاري التحميل..." FontSize="14" Foreground="#666" Margin="0,0,0,10"/>
                                
                                <TextBlock Text="🏗️ المشاريع:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="ProjectsCountText" Text="جاري التحميل..." FontSize="14" Foreground="#666"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                <TextBlock Text="🏢 القطاعات:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="SectorsCountText" Text="جاري التحميل..." FontSize="14" Foreground="#666" Margin="0,0,0,10"/>
                                
                                <TextBlock Text="👥 الموظفين:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="OfficersCountText" Text="جاري التحميل..." FontSize="14" Foreground="#666" Margin="0,0,0,10"/>
                                
                                <TextBlock Text="👤 المستخدمين:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="UsersCountText" Text="جاري التحميل..." FontSize="14" Foreground="#666"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- خيارات الاستعادة -->
                <Border Background="White" BorderBrush="#E1E8ED" BorderThickness="1"
                        CornerRadius="8" Padding="20" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="🔧 خيارات الاستعادة" FontSize="18" FontWeight="Bold"
                                 Foreground="#2C3E50" Margin="0,0,0,15"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <Button x:Name="RefreshButton" Content="🔄 تحديث المعلومات"
                                    Background="#17A2B8" Foreground="White"
                                    Padding="15,10" FontWeight="Bold" FontSize="14"
                                    BorderThickness="0" Margin="0,0,10,0"
                                    Click="RefreshButton_Click">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Cursor" Value="Hand"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#138496"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                            </Button>
                            
                            <Button x:Name="RestoreButton" Content="📥 استعادة البيانات"
                                    Background="#28A745" Foreground="White"
                                    Padding="15,10" FontWeight="Bold" FontSize="14"
                                    BorderThickness="0" Margin="0,0,10,0"
                                    Click="RestoreButton_Click">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Cursor" Value="Hand"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#218838"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                            </Button>
                            
                            <Button x:Name="ResetButton" Content="🔄 إعادة تعيين كاملة"
                                    Background="#DC3545" Foreground="White"
                                    Padding="15,10" FontWeight="Bold" FontSize="14"
                                    BorderThickness="0"
                                    Click="ResetButton_Click">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Cursor" Value="Hand"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#C82333"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                            </Button>
                        </StackPanel>
                        
                        <TextBlock Text="⚠️ تحذير: إعادة التعيين الكاملة ستحذف جميع البيانات الموجودة وتستعيد البيانات التجريبية فقط."
                                 FontSize="12" Foreground="#DC3545" TextWrapping="Wrap"/>
                    </StackPanel>
                </Border>

                <!-- سجل العمليات -->
                <Border Background="White" BorderBrush="#E1E8ED" BorderThickness="1"
                        CornerRadius="8" Padding="20">
                    <StackPanel>
                        <TextBlock Text="📝 سجل العمليات" FontSize="18" FontWeight="Bold"
                                 Foreground="#2C3E50" Margin="0,0,0,15"/>
                        
                        <ScrollViewer Height="200" VerticalScrollBarVisibility="Auto">
                            <TextBox x:Name="LogTextBox" IsReadOnly="True"
                                   Background="#F8F9FA" BorderThickness="0"
                                   FontFamily="Consolas" FontSize="12"
                                   TextWrapping="Wrap" AcceptsReturn="True"/>
                        </ScrollViewer>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E1E8ED" BorderThickness="0,1,0,0" Padding="20,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="❌ إغلاق"
                        Background="#6C757D" Foreground="White"
                        Padding="20,10" FontWeight="Bold" FontSize="14"
                        BorderThickness="0"
                        Click="CloseButton_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Cursor" Value="Hand"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#5A6268"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
