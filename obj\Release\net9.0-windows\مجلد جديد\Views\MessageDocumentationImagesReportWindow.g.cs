﻿#pragma checksum "..\..\..\..\..\مجلد جديد\Views\MessageDocumentationImagesReportWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F1F1A213F2D485B36E003CA6FA41B2B399162310"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DriverManagementSystem.Views {
    
    
    /// <summary>
    /// MessageDocumentationImagesReportWindow
    /// </summary>
    public partial class MessageDocumentationImagesReportWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 92 "..\..\..\..\..\مجلد جديد\Views\MessageDocumentationImagesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image Image1;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\..\مجلد جديد\Views\MessageDocumentationImagesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Image1Label;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\..\مجلد جديد\Views\MessageDocumentationImagesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel EmptyState1;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\..\مجلد جديد\Views\MessageDocumentationImagesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image Image2;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\..\مجلد جديد\Views\MessageDocumentationImagesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Image2Label;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\..\مجلد جديد\Views\MessageDocumentationImagesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel EmptyState2;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\..\مجلد جديد\Views\MessageDocumentationImagesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image Image3;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\..\مجلد جديد\Views\MessageDocumentationImagesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Image3Label;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\..\مجلد جديد\Views\MessageDocumentationImagesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel EmptyState3;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\..\مجلد جديد\Views\MessageDocumentationImagesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image Image4;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\..\مجلد جديد\Views\MessageDocumentationImagesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Image4Label;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\..\مجلد جديد\Views\MessageDocumentationImagesReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel EmptyState4;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri(("/SFDSystem;V2.0.0.0;component/%d9%85%d8%ac%d9%84%d8%af%20%d8%ac%d8%af%d9%8a%d8%af" +
                    "/views/messagedocumentationimagesreportwindow.xaml"), System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\مجلد جديد\Views\MessageDocumentationImagesReportWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.Image1 = ((System.Windows.Controls.Image)(target));
            return;
            case 2:
            this.Image1Label = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.EmptyState1 = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 4:
            this.Image2 = ((System.Windows.Controls.Image)(target));
            return;
            case 5:
            this.Image2Label = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.EmptyState2 = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 7:
            this.Image3 = ((System.Windows.Controls.Image)(target));
            return;
            case 8:
            this.Image3Label = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.EmptyState3 = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 10:
            this.Image4 = ((System.Windows.Controls.Image)(target));
            return;
            case 11:
            this.Image4Label = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.EmptyState4 = ((System.Windows.Controls.StackPanel)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

