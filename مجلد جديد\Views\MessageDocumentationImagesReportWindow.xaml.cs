using System;
using System.Collections.Generic;
using System.IO;
using System.Windows;
using System.Windows.Media.Imaging;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// نافذة عرض الصور المرفقة في توثيق الرسائل النصية
    /// </summary>
    public partial class MessageDocumentationImagesReportWindow : Window
    {
        private MessageDocumentation _documentation;
        private List<string> _imagePaths;

        public MessageDocumentationImagesReportWindow(MessageDocumentation documentation)
        {
            InitializeComponent();
            _documentation = documentation;
            LoadDocumentationData();
            LoadImages();
        }

        public MessageDocumentationImagesReportWindow(MessageDocumentation documentation, List<string> imagePaths)
        {
            InitializeComponent();
            _documentation = documentation;
            _imagePaths = imagePaths;
            LoadDocumentationData();
            LoadCustomImages();
        }

        /// <summary>
        /// تحميل بيانات التوثيق الأساسية
        /// </summary>
        private void LoadDocumentationData()
        {
            try
            {
                DataContext = new
                {
                    ReportNumber = _documentation?.ReportNumber ?? "غير محدد",
                    VisitNumber = _documentation?.VisitNumber ?? "غير محدد"
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل بيانات التوثيق: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل الصور المحفوظة
        /// </summary>
        private void LoadImages()
        {
            try
            {
                // تحميل الصورة الأولى
                LoadImage(_documentation?.ImagePath1, Image1, Image1Label, EmptyState1, "الصورة الأولى");
                
                // تحميل الصورة الثانية
                LoadImage(_documentation?.ImagePath2, Image2, Image2Label, EmptyState2, "الصورة الثانية");
                
                // تحميل الصورة الثالثة
                LoadImage(_documentation?.ImagePath3, Image3, Image3Label, EmptyState3, "الصورة الثالثة");
                
                // تحميل الصورة الرابعة
                LoadImage(_documentation?.ImagePath4, Image4, Image4Label, EmptyState4, "الصورة الرابعة");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الصور: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل صورة واحدة
        /// </summary>
        private void LoadImage(string imagePath, System.Windows.Controls.Image imageControl, 
            System.Windows.Controls.TextBlock labelControl, System.Windows.Controls.StackPanel emptyStateControl, 
            string defaultLabel)
        {
            try
            {
                if (!string.IsNullOrEmpty(imagePath) && File.Exists(imagePath))
                {
                    // تحميل الصورة
                    var bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = new Uri(imagePath, UriKind.Absolute);
                    bitmap.CacheOption = BitmapCacheOption.OnLoad;
                    bitmap.EndInit();
                    
                    imageControl.Source = bitmap;
                    imageControl.Visibility = Visibility.Visible;
                    
                    // تحديث التسمية بناءً على اسم الملف
                    var fileName = Path.GetFileNameWithoutExtension(imagePath);
                    labelControl.Text = !string.IsNullOrEmpty(fileName) ? fileName : defaultLabel;
                    labelControl.Visibility = Visibility.Visible;
                    
                    // إخفاء حالة الفراغ
                    emptyStateControl.Visibility = Visibility.Collapsed;
                }
                else
                {
                    // إظهار حالة الفراغ
                    imageControl.Visibility = Visibility.Collapsed;
                    labelControl.Visibility = Visibility.Collapsed;
                    emptyStateControl.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الصورة {imagePath}: {ex.Message}");
                
                // إظهار حالة الفراغ في حالة الخطأ
                imageControl.Visibility = Visibility.Collapsed;
                labelControl.Visibility = Visibility.Collapsed;
                emptyStateControl.Visibility = Visibility.Visible;
            }
        }

        /// <summary>
        /// طباعة الصفحة
        /// </summary>
        public void PrintPage()
        {
            try
            {
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    // تحديد حجم الطباعة
                    var pageSize = new Size(printDialog.PrintableAreaWidth, printDialog.PrintableAreaHeight);
                    
                    // تحضير النافذة للطباعة
                    this.Measure(pageSize);
                    this.Arrange(new Rect(pageSize));
                    
                    // طباعة النافذة
                    printDialog.PrintVisual(this, "توثيق الرسائل النصية - الصور المرفقة");
                    
                    MessageBox.Show("تم إرسال الصفحة للطباعة بنجاح", "طباعة", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حفظ الصفحة كصورة
        /// </summary>
        public void SaveAsImage(string filePath)
        {
            try
            {
                // تحديد حجم الصفحة (A4 بدقة 300 DPI)
                var pageSize = new Size(2480, 3508);
                
                // تحضير النافذة للحفظ
                this.Measure(pageSize);
                this.Arrange(new Rect(pageSize));
                
                // إنشاء RenderTargetBitmap
                var renderBitmap = new RenderTargetBitmap(
                    (int)pageSize.Width, (int)pageSize.Height, 300, 300, System.Windows.Media.PixelFormats.Pbgra32);
                
                renderBitmap.Render(this);
                
                // حفظ الصورة
                var encoder = new System.Windows.Media.Imaging.PngBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(renderBitmap));
                
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    encoder.Save(fileStream);
                }
                
                MessageBox.Show($"تم حفظ الصفحة بنجاح في:\n{filePath}", "حفظ",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الصفحة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل الصور المخصصة من قائمة المسارات
        /// </summary>
        private void LoadCustomImages()
        {
            try
            {
                if (_imagePaths != null && _imagePaths.Count > 0)
                {
                    // تحميل الصورة الأولى إذا كانت موجودة
                    if (_imagePaths.Count > 0 && File.Exists(_imagePaths[0]))
                    {
                        var bitmap = new BitmapImage(new Uri(_imagePaths[0]));
                        Image1.Source = bitmap;
                    }

                    // تحميل الصورة الثانية إذا كانت موجودة
                    if (_imagePaths.Count > 1 && File.Exists(_imagePaths[1]))
                    {
                        var bitmap = new BitmapImage(new Uri(_imagePaths[1]));
                        Image2.Source = bitmap;
                    }

                    // تحميل الصورة الثالثة إذا كانت موجودة
                    if (_imagePaths.Count > 2 && File.Exists(_imagePaths[2]))
                    {
                        var bitmap = new BitmapImage(new Uri(_imagePaths[2]));
                        Image3.Source = bitmap;
                    }

                    // تحميل الصورة الرابعة إذا كانت موجودة
                    if (_imagePaths.Count > 3 && File.Exists(_imagePaths[3]))
                    {
                        var bitmap = new BitmapImage(new Uri(_imagePaths[3]));
                        Image4.Source = bitmap;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الصور المخصصة: {ex.Message}");
            }
        }
    }
}
