using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    public partial class LoginWindow : Window
    {
        private readonly LoginViewModel _viewModel;
        private readonly DispatcherTimer _clockTimer;

        public LoginWindow()
        {
            InitializeComponent();

            var authService = new AuthenticationService();
            _viewModel = new LoginViewModel(authService);
            DataContext = _viewModel;

            _viewModel.LoginCompleted += OnLoginCompleted;

            // Initialize timer for clock and last login update
            _clockTimer = new DispatcherTimer();
            _clockTimer.Interval = TimeSpan.FromSeconds(1);
            _clockTimer.Tick += UpdateClockAndLastLogin;
            _clockTimer.Start();

            // Bind password box to view model
            PasswordBox.PasswordChanged += (s, e) =>
            {
                _viewModel.Password = PasswordBox.Password;
            };

            // Set focus to username textbox
            Loaded += (s, e) =>
            {
                UsernameTextBox.Focus();
                // Set default values for testing
                UsernameTextBox.Text = "admin";
                PasswordBox.Password = "123456";
                _viewModel.Username = "admin";
                _viewModel.Password = "123456";

                // Update clock and last login time
                UpdateClockAndLastLogin(null, null);
            };
        }

        private void LoginButton_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            // Show loading indicator
            MainLoginButton.IsEnabled = false;
            LoadingPanel.Visibility = Visibility.Visible;

            // Update password from PasswordBox
            _viewModel.Password = PasswordBox.Password;

            // Execute login command
            if (_viewModel.LoginCommand.CanExecute())
            {
                _viewModel.LoginCommand.Execute();
            }

            // Simulate login delay for better UX
            var timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(1.5);
            timer.Tick += (s, args) =>
            {
                timer.Stop();
                MainLoginButton.IsEnabled = true;
                LoadingPanel.Visibility = Visibility.Collapsed;
            };
            timer.Start();
        }

        private void UpdateClockAndLastLogin(object sender, EventArgs e)
        {
            // Update digital clock
            var now = DateTime.Now;
            var arabicClock = now.ToString("dddd، dd MMMM yyyy - hh:mm:ss tt",
                new System.Globalization.CultureInfo("ar-SA"));
            ClockText.Text = arabicClock;

            // Get last login from settings or default
            var lastLogin = Properties.Settings.Default.LastLoginTime;
            if (lastLogin == DateTime.MinValue)
            {
                lastLogin = DateTime.Now.AddDays(-1); // Default to yesterday
            }

            // Format the last login date in Arabic
            var arabicDate = lastLogin.ToString("dddd، dd MMMM yyyy - hh:mm tt",
                new System.Globalization.CultureInfo("ar-SA"));

            LastLoginText.Text = arabicDate;
        }

        private void OnLoginCompleted(object? sender, bool success)
        {
            if (success)
            {
                // Save last login time
                Properties.Settings.Default.LastLoginTime = DateTime.Now;
                Properties.Settings.Default.Save();

                // Show success message briefly
                LoadingPanel.Visibility = Visibility.Collapsed;
                var successPanel = new StackPanel
                {
                    Orientation = System.Windows.Controls.Orientation.Horizontal,
                    HorizontalAlignment = HorizontalAlignment.Center
                };
                successPanel.Children.Add(new TextBlock { Text = "✅", FontSize = 16, Margin = new Thickness(0, 0, 8, 0) });
                successPanel.Children.Add(new TextBlock { Text = "تم تسجيل الدخول بنجاح!", FontSize = 14, Foreground = System.Windows.Media.Brushes.Green });

                // Open main window
                var mainWindow = new MainWindow();
                mainWindow.Show();
                this.Close();
            }
            else
            {
                // Hide loading and show error
                MainLoginButton.IsEnabled = true;
                LoadingPanel.Visibility = Visibility.Collapsed;
            }
        }

        protected override void OnClosed(System.EventArgs e)
        {
            _viewModel.LoginCompleted -= OnLoginCompleted;
            _clockTimer?.Stop();
            base.OnClosed(e);
        }
    }
}
