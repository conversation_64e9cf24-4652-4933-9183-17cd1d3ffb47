using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using Microsoft.Win32;
using System.Printing;
using iTextSharp.text;
using iTextSharp.text.pdf;
using System.Windows.Markup;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Documents;
using DriverManagementSystem.Views;
using PdfParagraph = iTextSharp.text.Paragraph;

namespace DriverManagementSystem.Views
{
    public partial class PrintPreviewWindow : Window
    {
        private FrameworkElement _reportContent;

        public PrintPreviewWindow(FrameworkElement reportContent)
        {
            InitializeComponent();
            _reportContent = reportContent;
            LoadPreview();
        }

        private void LoadPreview()
        {
            try
            {
                // Clone the report content for preview
                var clonedContent = CloneReportContent(_reportContent);
                ReportContentPresenter.Content = clonedContent;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المعاينة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private FrameworkElement CloneReportContent(FrameworkElement original)
        {
            // Preview dimensions - Full size for better visibility
            double previewWidth = 780;   // Larger width to prevent cropping
            double previewHeight = 1080; // Full height

            // Create a new ReportView for preview
            var clonedReport = new ReportView
            {
                DataContext = original.DataContext,
                Width = previewWidth,
                Height = previewHeight,
                HorizontalAlignment = HorizontalAlignment.Stretch,
                VerticalAlignment = VerticalAlignment.Stretch
            };

            // Force layout update for preview
            clonedReport.Measure(new Size(previewWidth, previewHeight));
            clonedReport.Arrange(new Rect(0, 0, previewWidth, previewHeight));
            clonedReport.UpdateLayout();

            return clonedReport;
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                PrintReport();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SavePdfButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SaveAsPdf();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ PDF: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void PrintReport()
        {
            PrintDialog printDialog = new PrintDialog();

            // محاولة تحديد Microsoft Print to PDF كطابعة افتراضية
            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ البحث عن Microsoft Print to PDF...");

                // البحث عن Microsoft Print to PDF في قائمة الطابعات
                var printQueues = new System.Printing.LocalPrintServer().GetPrintQueues();
                var microsoftPrintToPdf = printQueues.FirstOrDefault(pq =>
                    pq.Name.Contains("Microsoft Print to PDF", StringComparison.OrdinalIgnoreCase));

                if (microsoftPrintToPdf != null)
                {
                    printDialog.PrintQueue = microsoftPrintToPdf;
                    System.Diagnostics.Debug.WriteLine("✅ تم تحديد Microsoft Print to PDF كطابعة افتراضية");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على Microsoft Print to PDF، سيتم عرض جميع الطابعات");
                }

                // تحديد A4 مسبقاً
                var a4Size = new System.Printing.PageMediaSize(System.Printing.PageMediaSizeName.ISOA4, 210, 297);
                printDialog.PrintTicket.PageMediaSize = a4Size;
                printDialog.PrintTicket.PageOrientation = System.Printing.PageOrientation.Portrait;

                System.Diagnostics.Debug.WriteLine("✅ تم تحديد A4 للطابعة المختارة في PrintPreview");
            }
            catch (Exception preEx)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ تعذر تحديد الطابعة في PrintPreview: {preEx.Message}");
            }

            if (printDialog.ShowDialog() == true)
            {
                // تحديد إعدادات الطباعة A4 بقوة
                try
                {
                    // تحديد حجم الصفحة A4 بطرق متعددة لضمان التطبيق
                    if (printDialog.PrintTicket != null)
                    {
                        printDialog.PrintTicket.PageMediaSize = new System.Printing.PageMediaSize(
                            System.Printing.PageMediaSizeName.ISOA4, 210, 297);
                        printDialog.PrintTicket.PageOrientation = System.Printing.PageOrientation.Portrait;

                        // تأكيد إضافي لحجم A4
                        var a4Size = new System.Printing.PageMediaSize(System.Printing.PageMediaSizeName.ISOA4);
                        printDialog.PrintTicket.PageMediaSize = a4Size;

                        System.Diagnostics.Debug.WriteLine("✅ تم تحديد إعدادات الطباعة A4 بقوة في PrintPreview");
                    }
                }
                catch (Exception pageEx)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ تعذر تحديد إعدادات الطباعة في PrintPreview: {pageEx.Message}");

                    // محاولة بديلة لتحديد A4
                    try
                    {
                        printDialog.PrintQueue.DefaultPrintTicket.PageMediaSize =
                            new System.Printing.PageMediaSize(System.Printing.PageMediaSizeName.ISOA4);
                        System.Diagnostics.Debug.WriteLine("✅ تم تحديد A4 عبر PrintQueue في PrintPreview");
                    }
                    catch (Exception fallbackEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ فشل في الطريقة البديلة في PrintPreview: {fallbackEx.Message}");
                    }
                }

                try
                {
                    System.Diagnostics.Debug.WriteLine($"🖨️ بدء الطباعة باستخدام: {printDialog.PrintQueue.Name}");

                    // إذا كان المستخدم اختار Microsoft Print to PDF، استخدم النظام المحسن
                    if (printDialog.PrintQueue.Name.Contains("Microsoft Print to PDF", StringComparison.OrdinalIgnoreCase))
                    {
                        System.Diagnostics.Debug.WriteLine("📄 المستخدم اختار Microsoft Print to PDF، سيتم استخدام النظام المحسن...");

                        SaveFileDialog saveDialog = new SaveFileDialog
                        {
                            Filter = "PDF Files (*.pdf)|*.pdf",
                            DefaultExt = "pdf",
                            FileName = $"محضر_استخراج_عروض_الأسعار_{DateTime.Now:yyyy-MM-dd_HH-mm}.pdf"
                        };

                        if (saveDialog.ShowDialog() == true)
                        {
                            // الحصول على البيانات الأصلية من ViewModel
                            var reportViewModel = _reportContent.DataContext as ViewModels.ReportViewModel;
                            if (reportViewModel?.ReportData != null)
                            {
                                CreateDataBasedPdf(reportViewModel.ReportData, saveDialog.FileName);
                                MessageBox.Show($"تم حفظ PDF بنجاح باستخدام Microsoft Print to PDF!\n\n📁 المسار: {saveDialog.FileName}\n📐 المقاس: A4 (210×297 مم)\n🖨️ جودة عالية مع البيانات الأصلية\n📄 صفحات متعددة في ملف واحد", "نجح الحفظ",
                                    MessageBoxButton.OK, MessageBoxImage.Information);
                            }
                            else
                            {
                                MessageBox.Show("لا توجد بيانات تقرير متاحة للحفظ", "خطأ",
                                    MessageBoxButton.OK, MessageBoxImage.Error);
                            }
                        }
                        return;
                    }

                    // للطابعات العادية، استخدم النظام التقليدي
                    System.Diagnostics.Debug.WriteLine("🖨️ استخدام النظام التقليدي للطابعات العادية...");

                    var printDocument = CreateCompletePrintDocument(_reportContent);
                    if (printDocument != null)
                    {
                        printDialog.PrintVisual(printDocument, "تقرير الزيارة الميدانية - مستند كامل");
                        var reportPages = FindReportPages(_reportContent);
                        var pageCount = reportPages?.Count ?? 1;
                        MessageBox.Show($"تم إرسال التقرير للطباعة بنجاح!\n📄 عدد الصفحات: {pageCount}\n🖨️ الطابعة: {printDialog.PrintQueue.Name}\n📐 حجم الطباعة: A4", "طباعة",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        var printVersion = CreatePrintVersion();
                        printDialog.PrintVisual(printVersion, "تقرير الزيارة الميدانية");
                        MessageBox.Show($"تم إرسال التقرير للطباعة بنجاح!\n🖨️ الطابعة: {printDialog.PrintQueue.Name}", "طباعة",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في نظام الطباعة: {ex.Message}");

                    // في حالة الخطأ، استخدم الطريقة الأساسية
                    try
                    {
                        var printVersion = CreatePrintVersion();
                        printDialog.PrintVisual(printVersion, "تقرير الزيارة الميدانية");

                        MessageBox.Show("تم إرسال التقرير للطباعة بنجاح!\n📋 تحقق من طابعتك لاستلام المستندات.", "طباعة",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception fallbackEx)
                    {
                        MessageBox.Show($"خطأ في الطباعة: {fallbackEx.Message}\n\nتأكد من أن الطابعة متصلة وتعمل بشكل صحيح.", "خطأ في الطباعة",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private FrameworkElement CreatePrintVersion()
        {
            // A4 exact dimensions for printing - ملء الصفحة بالكامل
            double a4Width = 794;   // 8.27 inches × 96 DPI
            double a4Height = 1123; // 11.69 inches × 96 DPI

            // Create full-size container for printing - بدون حدود لملء الصفحة
            var printContainer = new Border
            {
                Background = Brushes.White,
                Width = a4Width,
                Height = a4Height,
                BorderThickness = new Thickness(0),
                Padding = new Thickness(10) // هوامش صغيرة فقط
            };

            // Create full-size report content - ملء الصفحة بالكامل
            var printReport = new ReportView
            {
                DataContext = _reportContent.DataContext,
                Width = a4Width - 20,  // هوامش صغيرة جداً
                Height = a4Height - 20,
                HorizontalAlignment = HorizontalAlignment.Stretch,
                VerticalAlignment = VerticalAlignment.Stretch
            };

            printContainer.Child = printReport;

            // Force layout for printing
            printContainer.Measure(new Size(a4Width, a4Height));
            printContainer.Arrange(new Rect(0, 0, a4Width, a4Height));
            printContainer.UpdateLayout();

            return printContainer;
        }

        private void SaveAsPdf()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 بدء حفظ PDF باستخدام Microsoft Print to PDF...");

                // الحصول على البيانات الأصلية من ViewModel
                var reportViewModel = _reportContent.DataContext as ViewModels.ReportViewModel;
                if (reportViewModel?.ReportData == null)
                {
                    MessageBox.Show("لا توجد بيانات تقرير متاحة للحفظ", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // إنشاء اسم الملف
                var fileName = $"محضر_استخراج_عروض_الأسعار_{DateTime.Now:yyyy-MM-dd_HH-mm}.pdf";
                var desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                var fullPath = Path.Combine(desktopPath, fileName);

                // إنشاء PDF باستخدام البيانات الأصلية مباشرة
                CreateDataBasedPdf(reportViewModel.ReportData, fullPath);

                MessageBox.Show($"تم حفظ PDF بنجاح!\n\n📁 المسار: {fullPath}\n📄 تم إنشاء صفحات متعددة\n📐 المقاس: A4 (210×297 مم)\n🖨️ جودة عالية باستخدام البيانات الأصلية\n\n✅ الملف جاهز للطباعة أو المشاركة", "نجح الحفظ",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                // Ask if user wants to open the file
                var result = MessageBox.Show("هل تريد فتح الملف الآن؟", "فتح الملف",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = fullPath,
                        UseShellExecute = true
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ PDF: {ex.Message}");
                MessageBox.Show($"خطأ في حفظ PDF: {ex.Message}\n\nتأكد من أن لديك صلاحيات الكتابة على سطح المكتب", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private List<System.Windows.Controls.Border> FindReportPages(FrameworkElement reportView)
        {
            var pages = new List<System.Windows.Controls.Border>();

            try
            {
                System.Diagnostics.Debug.WriteLine("🔍 بدء البحث عن صفحات التقرير في PrintPreview...");

                // البحث عن StackPanel الرئيسي
                var mainStackPanel = FindChild<StackPanel>(reportView);

                if (mainStackPanel != null)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم العثور على StackPanel الرئيسي مع {mainStackPanel.Children.Count} عنصر فرعي");

                    // البحث عن جميع Border التي تحتوي على صفحات التقرير
                    foreach (var child in mainStackPanel.Children)
                    {
                        if (child is System.Windows.Controls.Border border)
                        {
                            bool isReportPage = false;

                            // طريقة 1: التحقق من Style (PrintPageStyle)
                            if (border.Style != null)
                            {
                                try
                                {
                                    var printPageStyle = border.TryFindResource("PrintPageStyle");
                                    if (printPageStyle != null && border.Style == printPageStyle)
                                    {
                                        isReportPage = true;
                                        System.Diagnostics.Debug.WriteLine($"✅ تم العثور على صفحة تقرير بـ PrintPageStyle");
                                    }
                                }
                                catch
                                {
                                    var styleKey = border.Style.ToString();
                                    if (styleKey.Contains("PrintPageStyle"))
                                    {
                                        isReportPage = true;
                                        System.Diagnostics.Debug.WriteLine($"✅ تم العثور على صفحة تقرير بـ Style: {styleKey}");
                                    }
                                }
                            }

                            // طريقة 2: التحقق من خلال المحتوى
                            if (!isReportPage && border.Child is StackPanel stackPanel)
                            {
                                if (stackPanel.Children.Count > 3)
                                {
                                    isReportPage = true;
                                    System.Diagnostics.Debug.WriteLine($"✅ تم العثور على صفحة تقرير بالمحتوى: {stackPanel.Children.Count} عناصر");
                                }
                            }

                            // طريقة 3: التحقق من خلال الخصائص
                            if (!isReportPage)
                            {
                                var margin = border.Margin;
                                if ((margin.Top >= 5 && margin.Bottom >= 5) ||
                                    border.ActualWidth > 500 || border.ActualHeight > 600)
                                {
                                    if (border.Height != 30 &&
                                        border.Background != System.Windows.Media.Brushes.Transparent)
                                    {
                                        isReportPage = true;
                                        System.Diagnostics.Debug.WriteLine($"✅ تم العثور على صفحة تقرير بالخصائص");
                                    }
                                }
                            }

                            if (isReportPage)
                            {
                                pages.Add(border);
                            }
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"📊 إجمالي الصفحات المكتشفة في PrintPreview: {pages.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في البحث عن صفحات التقرير في PrintPreview: {ex.Message}");
            }

            return pages;
        }

        private T FindChild<T>(DependencyObject parent) where T : DependencyObject
        {
            if (parent == null) return null;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);

                if (child is T result)
                    return result;

                var childOfChild = FindChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }

            return null;
        }



        private FrameworkElement CreateCompletePrintDocument(FrameworkElement reportView)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 إنشاء مستند طباعة شامل من PrintPreview...");

                // البحث عن جميع صفحات التقرير
                var reportPages = FindReportPages(reportView);

                if (reportPages?.Any() == true)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم العثور على {reportPages.Count} صفحة");

                    // إنشاء حاوي رئيسي محسن لجميع الصفحات بحجم A4
                    var mainContainer = new StackPanel
                    {
                        Background = System.Windows.Media.Brushes.White,
                        Orientation = Orientation.Vertical,
                        Width = 816,  // عرض A4 محسن (8.5" × 96 DPI)
                        HorizontalAlignment = HorizontalAlignment.Center,
                        FlowDirection = FlowDirection.RightToLeft, // اتجاه عربي صحيح
                        Margin = new Thickness(0)
                    };

                    // إضافة كل صفحة إلى الحاوي الرئيسي
                    for (int i = 0; i < reportPages.Count; i++)
                    {
                        var page = reportPages[i];

                        // إنشاء نسخة محسنة من الصفحة للطباعة
                        var pageClone = CreateEnhancedPrintablePage(page);

                        if (pageClone != null)
                        {
                            // إضافة الصفحة إلى الحاوي
                            mainContainer.Children.Add(pageClone);

                            // إضافة فاصل صفحة إذا لم تكن الصفحة الأخيرة
                            if (i < reportPages.Count - 1)
                            {
                                var pageBreak = new Border
                                {
                                    Height = 50,
                                    Background = System.Windows.Media.Brushes.Transparent
                                };
                                mainContainer.Children.Add(pageBreak);
                            }
                        }
                    }

                    // تحديث تخطيط الحاوي
                    mainContainer.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
                    mainContainer.Arrange(new Rect(mainContainer.DesiredSize));
                    mainContainer.UpdateLayout();

                    System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء مستند شامل يحتوي على {reportPages.Count} صفحة من PrintPreview");
                    return mainContainer;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على صفحات منفصلة في PrintPreview");
                    return reportView; // إرجاع التقرير الأصلي
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء المستند الشامل من PrintPreview: {ex.Message}");
                return null;
            }
        }

        private FrameworkElement ClonePageForPrint(System.Windows.Controls.Border originalPage)
        {
            try
            {
                // إنشاء نسخة من الصفحة للطباعة
                var xaml = System.Windows.Markup.XamlWriter.Save(originalPage);
                var clonedPage = (FrameworkElement)System.Windows.Markup.XamlReader.Parse(xaml);

                // تطبيق خصائص الطباعة A4
                clonedPage.Width = 794;   // عرض A4 (210mm)
                clonedPage.Height = 1123; // ارتفاع A4 (297mm)
                clonedPage.HorizontalAlignment = HorizontalAlignment.Center;
                clonedPage.VerticalAlignment = VerticalAlignment.Top;

                // تحديث التخطيط
                clonedPage.Measure(new Size(794, 1123));
                clonedPage.Arrange(new Rect(0, 0, 794, 1123));
                clonedPage.UpdateLayout();

                System.Diagnostics.Debug.WriteLine($"✅ تم نسخ الصفحة بحجم A4 في PrintPreview: 794×1123");
                return clonedPage;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ فشل في نسخ الصفحة في PrintPreview: {ex.Message}");
                return originalPage; // إرجاع الصفحة الأصلية
            }
        }

        /// <summary>
        /// إنشاء PDF متعدد الصفحات باستخدام البيانات الأصلية مباشرة
        /// </summary>
        private void CreateMultiPagePdf(List<Border> pages, string fileName)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"📄 بدء إنشاء PDF متعدد الصفحات مع البيانات الأصلية...");

                // الحصول على البيانات الأصلية من ViewModel
                var reportViewModel = _reportContent.DataContext as ViewModels.ReportViewModel;
                if (reportViewModel?.ReportData == null)
                {
                    throw new Exception("لا توجد بيانات تقرير متاحة للطباعة");
                }

                // إنشاء PDF باستخدام البيانات الأصلية
                CreateDataBasedPdf(reportViewModel.ReportData, fileName);

                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء PDF متعدد الصفحات بنجاح: {fileName}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء PDF متعدد الصفحات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء PDF باستخدام البيانات الأصلية مباشرة من قاعدة البيانات
        /// </summary>
        private void CreateDataBasedPdf(Models.ReportModel reportData, string fileName)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 إنشاء PDF باستخدام البيانات الأصلية...");

                // إنشاء مستند PDF بحجم A4 - هوامش محسنة لتجنب الاقتصاص
                var document = new iTextSharp.text.Document(PageSize.A4, 15, 15, 15, 15);

                using (var stream = new FileStream(fileName, FileMode.Create))
                {
                    var writer = PdfWriter.GetInstance(document, stream);
                    document.Open();

                    // إنشاء الخطوط العربية - أحجام محسنة للدقة العالية
                    var arabicFont = CreateArabicFont();
                    var titleFont = new iTextSharp.text.Font(arabicFont, 28, iTextSharp.text.Font.BOLD); // حجم كبير جداً للوضوح
                    var headerFont = new iTextSharp.text.Font(arabicFont, 24, iTextSharp.text.Font.BOLD); // حجم كبير للوضوح
                    var normalFont = new iTextSharp.text.Font(arabicFont, 20, iTextSharp.text.Font.NORMAL); // حجم كبير للوضوح

                    // الصفحة الأولى: محضر استدراج عروض الأسعار
                    CreateReportPage(document, reportData, titleFont, headerFont, normalFont);
                    AddPageNumber(document, writer, 1); // إضافة رقم الصفحة الأولى

                    // الصفحات التالية: عقد إيجار السيارة
                    document.NewPage();
                    CreateContractPages(document, reportData, titleFont, headerFont, normalFont);
                    AddPageNumber(document, writer, 2); // إضافة رقم الصفحة الثانية

                    document.Close();
                }

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء PDF بالبيانات الأصلية بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء PDF بالبيانات الأصلية: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء نسخة محسنة من الصفحة للـ PDF
        /// </summary>
        private FrameworkElement CreateEnhancedPageForPdf(Border originalPage)
        {
            try
            {
                // إنشاء حاوي بحجم A4 محسن
                var pdfContainer = new Grid
                {
                    Width = 816,  // عرض A4 محسن (8.5" × 96 DPI)
                    Height = 1056, // ارتفاع A4 محسن (11" × 96 DPI)
                    Background = Brushes.White,
                    FlowDirection = FlowDirection.RightToLeft
                };

                // إنشاء Border داخلي مع هوامش
                var innerBorder = new Border
                {
                    Margin = new Thickness(24), // هوامش 0.25 بوصة
                    Background = Brushes.Transparent,
                    FlowDirection = FlowDirection.RightToLeft
                };

                // نسخ محتوى الصفحة الأصلية
                if (originalPage.Parent is Panel parentPanel)
                {
                    parentPanel.Children.Remove(originalPage);
                }

                originalPage.FlowDirection = FlowDirection.RightToLeft;
                innerBorder.Child = originalPage;
                pdfContainer.Children.Add(innerBorder);

                // تحديث التخطيط
                pdfContainer.Measure(new Size(816, 1056));
                pdfContainer.Arrange(new Rect(0, 0, 816, 1056));
                pdfContainer.UpdateLayout();

                return pdfContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء صفحة محسنة للـ PDF: {ex.Message}");
                return originalPage;
            }
        }

        /// <summary>
        /// تحويل صفحة إلى صورة
        /// </summary>
        private byte[] ConvertPageToImage(FrameworkElement page)
        {
            try
            {
                // إنشاء RenderTargetBitmap بدقة عالية
                var renderBitmap = new RenderTargetBitmap(
                    (int)page.Width,
                    (int)page.Height,
                    96, 96, // DPI
                    PixelFormats.Pbgra32);

                renderBitmap.Render(page);

                // تحويل إلى PNG
                var encoder = new PngBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(renderBitmap));

                using (var stream = new MemoryStream())
                {
                    encoder.Save(stream);
                    return stream.ToArray();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحويل الصفحة إلى صورة: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء خط عربي للـ PDF
        /// </summary>
        private BaseFont CreateArabicFont()
        {
            try
            {
                // استخدام خط عربي مدمج
                return BaseFont.CreateFont("c:/windows/fonts/arial.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            }
            catch
            {
                // في حالة فشل الخط العربي، استخدم الخط الافتراضي
                return BaseFont.CreateFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
            }
        }

        /// <summary>
        /// إنشاء صفحة المحضر
        /// </summary>
        private void CreateReportPage(iTextSharp.text.Document document, Models.ReportModel reportData,
            iTextSharp.text.Font titleFont, iTextSharp.text.Font headerFont, iTextSharp.text.Font normalFont)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 إنشاء صفحة المحضر...");

                // العنوان الرئيسي - حجم أكبر وتباعد أكبر لملء الصفحة
                var title = new PdfParagraph("محضر استدراج عروض الأسعار", titleFont)
                {
                    Alignment = Element.ALIGN_CENTER,
                    SpacingAfter = 30, // تباعد أكبر
                    SpacingBefore = 20
                };
                document.Add(title);

                // معلومات التاريخ والرقم - حجم أكبر وتباعد أكبر
                var dateInfo = new PdfParagraph($"التاريخ: {reportData.ReportDate} | رقم الزيارة: {reportData.VisitNumber}", headerFont)
                {
                    Alignment = Element.ALIGN_RIGHT,
                    SpacingAfter = 25, // تباعد أكبر
                    SpacingBefore = 10
                };
                document.Add(dateInfo);

                // جدول العروض
                CreateOffersTable(document, reportData, headerFont, normalFont);

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء صفحة المحضر بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء صفحة المحضر: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء جدول العروض
        /// </summary>
        private void CreateOffersTable(iTextSharp.text.Document document, Models.ReportModel reportData,
            iTextSharp.text.Font headerFont, iTextSharp.text.Font normalFont)
        {
            try
            {
                // إنشاء جدول بـ 3 أعمدة - ملء الصفحة بالكامل
                var table = new PdfPTable(3)
                {
                    WidthPercentage = 100,
                    SpacingBefore = 20, // تباعد أكبر
                    SpacingAfter = 20,
                    DefaultCell = { MinimumHeight = 25 } // ارتفاع أكبر للخلايا
                };

                // تحديد عرض الأعمدة - توزيع أفضل لملء الصفحة
                table.SetWidths(new float[] { 1.2f, 4f, 2f });

                // رؤوس الجدول
                table.AddCell(CreateTableCell("م", headerFont, true));
                table.AddCell(CreateTableCell("اسم السائق", headerFont, true));
                table.AddCell(CreateTableCell("السعر المقترح", headerFont, true));

                // إضافة بيانات العروض
                if (reportData.PriceOffers?.Any() == true)
                {
                    foreach (var offer in reportData.PriceOffers)
                    {
                        table.AddCell(CreateTableCell(offer.SerialNumber.ToString(), normalFont, false));
                        table.AddCell(CreateTableCell(offer.DriverName ?? "غير محدد", normalFont, false));
                        table.AddCell(CreateTableCell(offer.OfferedPrice.ToString("N0") + " ريال", normalFont, false));
                    }
                }
                else
                {
                    // إضافة صف فارغ إذا لم توجد بيانات
                    table.AddCell(CreateTableCell("1", normalFont, false));
                    table.AddCell(CreateTableCell("لا توجد عروض أسعار", normalFont, false));
                    table.AddCell(CreateTableCell("0 ريال", normalFont, false));
                }

                document.Add(table);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء جدول العروض: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء خلية جدول
        /// </summary>
        private PdfPCell CreateTableCell(string text, iTextSharp.text.Font font, bool isHeader)
        {
            var cell = new PdfPCell(new Phrase(text, font))
            {
                HorizontalAlignment = Element.ALIGN_CENTER,
                VerticalAlignment = Element.ALIGN_MIDDLE,
                Padding = 15, // حشو أكبر لملء الصفحة
                Border = Rectangle.BOX,
                MinimumHeight = 35 // ارتفاع أكبر للخلايا
            };

            if (isHeader)
            {
                cell.BackgroundColor = new BaseColor(220, 220, 220); // رمادي فاتح
                cell.MinimumHeight = 40; // ارتفاع أكبر للرؤوس
            }

            return cell;
        }

        /// <summary>
        /// إضافة رقم الصفحة في أسفل اليمين بشكل احترافي
        /// </summary>
        private void AddPageNumber(iTextSharp.text.Document document, PdfWriter writer, int pageNumber)
        {
            try
            {
                // إنشاء خط صغير لرقم الصفحة
                var arabicFont = CreateArabicFont();
                var pageNumberFont = new iTextSharp.text.Font(arabicFont, 10, iTextSharp.text.Font.NORMAL);

                // إنشاء جدول لوضع رقم الصفحة في أسفل اليمين
                var pageNumberTable = new PdfPTable(1)
                {
                    WidthPercentage = 100,
                    TotalWidth = document.PageSize.Width - document.LeftMargin - document.RightMargin
                };

                // خلية رقم الصفحة
                var pageNumberCell = new PdfPCell(new Phrase(pageNumber.ToString(), pageNumberFont))
                {
                    Border = Rectangle.BOX,
                    BorderColor = new BaseColor(180, 180, 180),
                    BorderWidth = 0.5f,
                    BackgroundColor = new BaseColor(250, 250, 250),
                    HorizontalAlignment = Element.ALIGN_CENTER,
                    VerticalAlignment = Element.ALIGN_MIDDLE,
                    Padding = 6,
                    FixedHeight = 20
                };

                pageNumberTable.AddCell(pageNumberCell);

                // وضع الجدول في أسفل اليسار ومرفوع قليلاً
                pageNumberTable.WriteSelectedRows(0, -1,
                    document.LeftMargin + 5, // أسفل اليسار
                    document.BottomMargin + 35, // مرفوع قليلاً من 15 إلى 35
                    writer.DirectContent);

                System.Diagnostics.Debug.WriteLine($"✅ تم إضافة رقم الصفحة {pageNumber} في أسفل اليمين");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة رقم الصفحة: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء صفحات العقد
        /// </summary>
        private void CreateContractPages(iTextSharp.text.Document document, Models.ReportModel reportData,
            iTextSharp.text.Font titleFont, iTextSharp.text.Font headerFont, iTextSharp.text.Font normalFont)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 إنشاء صفحات العقد...");

                // عنوان العقد - حجم أكبر وتباعد أكبر لملء الصفحة
                var contractTitle = new PdfParagraph("عقد إيجار سيارة", titleFont)
                {
                    Alignment = Element.ALIGN_CENTER,
                    SpacingAfter = 35, // تباعد أكبر
                    SpacingBefore = 25
                };
                document.Add(contractTitle);

                // معلومات العقد - حجم أكبر وتباعد أكبر
                var contractInfo = new PdfParagraph($"رقم العقد: {reportData.VisitNumber} | التاريخ: {reportData.ContractDate}", headerFont)
                {
                    Alignment = Element.ALIGN_RIGHT,
                    SpacingAfter = 25, // تباعد أكبر
                    SpacingBefore = 15
                };
                document.Add(contractInfo);

                // تفاصيل العقد
                var contractDetails = new PdfParagraph(
                    $"تفاصيل العقد:\n" +
                    $"القطاع: {reportData.SectorName ?? "غير محدد"}\n" +
                    $"المسؤول عن الحركة: {reportData.MovementResponsibleName ?? "غير محدد"}\n" +
                    $"عدد الأيام: {reportData.DaysCount}\n" +
                    $"ملاحظات: {reportData.Notes ?? "لا توجد ملاحظات"}",
                    normalFont)
                {
                    Alignment = Element.ALIGN_RIGHT,
                    SpacingAfter = 20
                };
                document.Add(contractDetails);

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء صفحات العقد بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء صفحات العقد: {ex.Message}");
                throw;
            }
        }

        public void CreatePdfFromVisual(FrameworkElement visual, string fileName)
        {
            try
            {
                // Always use image-based PDF to capture exactly what's on screen
                CreateImageBasedPdf(visual, fileName);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء PDF: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CreateTextBasedPdf(iTextSharp.text.Document document, FrameworkElement visual)
        {
            // Get the report data
            var reportData = visual.DataContext as ViewModels.ReportViewModel;
            if (reportData?.ReportData == null) return;

            var data = reportData.ReportData;

            // Arabic font setup
            string fontPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Fonts), "arial.ttf");
            if (!File.Exists(fontPath))
            {
                fontPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "Fonts", "arial.ttf");
            }

            BaseFont arabicFont = BaseFont.CreateFont(fontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            var titleFont = new iTextSharp.text.Font(arabicFont, 18, iTextSharp.text.Font.BOLD);
            var headerFont = new iTextSharp.text.Font(arabicFont, 14, iTextSharp.text.Font.BOLD);
            var normalFont = new iTextSharp.text.Font(arabicFont, 12, iTextSharp.text.Font.NORMAL);
            var smallFont = new iTextSharp.text.Font(arabicFont, 10, iTextSharp.text.Font.NORMAL);

            // Header section
            var headerTable = new PdfPTable(2);
            headerTable.WidthPercentage = 100;
            headerTable.SetWidths(new float[] { 1, 1 });

            var dateCell = new PdfPCell(new Phrase($"التاريخ: {data.ReportDate}", normalFont));
            dateCell.Border = Rectangle.NO_BORDER;
            dateCell.HorizontalAlignment = Element.ALIGN_RIGHT;
            headerTable.AddCell(dateCell);

            var visitCell = new PdfPCell(new Phrase($"رقم الزيارة: {data.VisitNumber}", normalFont));
            visitCell.Border = Rectangle.NO_BORDER;
            visitCell.HorizontalAlignment = Element.ALIGN_LEFT;
            headerTable.AddCell(visitCell);

            document.Add(headerTable);
            document.Add(new PdfParagraph(" "));

            // Organization info
            var orgPara = new PdfParagraph("الجمهورية اليمنية", headerFont);
            orgPara.Alignment = Element.ALIGN_CENTER;
            document.Add(orgPara);

            var deptPara = new PdfParagraph("المجلس المحلي للمديرية", normalFont);
            deptPara.Alignment = Element.ALIGN_CENTER;
            document.Add(deptPara);

            var branchPara = new PdfParagraph("فرع عدن والمحافظات", normalFont);
            branchPara.Alignment = Element.ALIGN_CENTER;
            document.Add(branchPara);

            document.Add(new PdfParagraph(" "));

            // Title
            var titlePara = new PdfParagraph("محضر استخراج عروض أسعار", titleFont);
            titlePara.Alignment = Element.ALIGN_CENTER;
            titlePara.SpacingAfter = 20;
            document.Add(titlePara);

            // Projects section
            var projectsTitle = new PdfParagraph("المشاريع التي سيتم زيارتها", headerFont);
            projectsTitle.SpacingBefore = 10;
            document.Add(projectsTitle);

            if (data.Projects != null && data.Projects.Count > 0)
            {
                foreach (var project in data.Projects)
                {
                    var projectPara = new PdfParagraph($"{project.ProjectNumber} - {project.ProjectName}", normalFont);
                    projectPara.IndentationLeft = 20;
                    document.Add(projectPara);
                }
            }

            document.Add(new PdfParagraph(" "));

            // Technical data section
            var techTitle = new PdfParagraph("البيانات الفنية", headerFont);
            document.Add(techTitle);

            document.Add(new PdfParagraph($"طبيعة التشغيل: {data.VisitNature}", normalFont));
            document.Add(new PdfParagraph($"القائم بالزيارة: {data.VisitConductor}", normalFont));
            document.Add(new PdfParagraph($"خط السير: {data.RouteDescription}", normalFont));

            document.Add(new PdfParagraph(" "));

            // Price offers table
            var offersTitle = new PdfParagraph("قائمة الأسعار المقدمة من السائقين", headerFont);
            document.Add(offersTitle);

            if (data.PriceOffers != null && data.PriceOffers.Count > 0)
            {
                var offersTable = new PdfPTable(4);
                offersTable.WidthPercentage = 100;
                offersTable.SetWidths(new float[] { 1, 3, 2, 2 });

                // Headers
                offersTable.AddCell(new PdfPCell(new Phrase("الرقم", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                offersTable.AddCell(new PdfPCell(new Phrase("اسم السائق", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                offersTable.AddCell(new PdfPCell(new Phrase("رقم التلفون", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                offersTable.AddCell(new PdfPCell(new Phrase("المبلغ", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });

                // Data
                foreach (var offer in data.PriceOffers)
                {
                    offersTable.AddCell(new PdfPCell(new Phrase(offer.SerialNumber.ToString(), normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                    offersTable.AddCell(new PdfPCell(new Phrase(offer.DriverName, normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                    offersTable.AddCell(new PdfPCell(new Phrase(offer.PhoneNumber, normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                    offersTable.AddCell(new PdfPCell(new Phrase(offer.OfferedPrice.ToString("N0"), normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                }

                document.Add(offersTable);
            }

            document.Add(new PdfParagraph(" "));

            // Duration section
            var durationTable = new PdfPTable(3);
            durationTable.WidthPercentage = 100;

            durationTable.AddCell(new PdfPCell(new Phrase($"تاريخ النزول: {data.DepartureDate}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });
            durationTable.AddCell(new PdfPCell(new Phrase($"تاريخ العودة: {data.ReturnDate}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });
            durationTable.AddCell(new PdfPCell(new Phrase($"عدد الأيام: {data.DaysCount}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });

            document.Add(durationTable);
            document.Add(new PdfParagraph(" "));

            // Selected driver section
            var driverTitle = new PdfParagraph("السائق المختار وبيانات السيارة", headerFont);
            document.Add(driverTitle);

            var driverTable = new PdfPTable(2);
            driverTable.WidthPercentage = 100;

            driverTable.AddCell(new PdfPCell(new Phrase($"السائق: {data.SelectedDriverName}", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase($"سنة الصنع: {data.VehicleModel}", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase($"التلفون: {data.SelectedDriverPhone}", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase($"اللون: {data.VehicleColor}", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase($"نوع السيارة: {data.VehicleType}", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase($"رقم اللوحة: {data.PlateNumber}", normalFont)) { Border = Rectangle.NO_BORDER });

            document.Add(driverTable);
            document.Add(new PdfParagraph(" "));

            // Signatures section
            var signaturesTable = new PdfPTable(3);
            signaturesTable.WidthPercentage = 100;
            signaturesTable.SpacingBefore = 30;

            signaturesTable.AddCell(new PdfPCell(new Phrase("المكلف بالمهمة", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });
            signaturesTable.AddCell(new PdfPCell(new Phrase("مسئول الحركة", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });
            signaturesTable.AddCell(new PdfPCell(new Phrase("يعتمد", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });

            signaturesTable.AddCell(new PdfPCell(new Phrase($"{data.TaskManagerName}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER, PaddingTop = 30 });
            signaturesTable.AddCell(new PdfPCell(new Phrase($"{data.MovementResponsibleName}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER, PaddingTop = 30 });
            signaturesTable.AddCell(new PdfPCell(new Phrase($"{data.BranchManagerName}\n{data.BranchManagerTitle}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER, PaddingTop = 30 });

            document.Add(signaturesTable);
        }

        private void CreateImageBasedPdf(FrameworkElement visual, string fileName)
        {
            // Create PDF with exact A4 dimensions (210x297mm)
            var document = new iTextSharp.text.Document(PageSize.A4, 0, 0, 0, 0); // No margins for full page

            using (var stream = new FileStream(fileName, FileMode.Create))
            {
                var writer = PdfWriter.GetInstance(document, stream);
                document.Open();

                // A4 dimensions at 300 DPI for high quality print
                // A4 = 210×297mm = 8.27×11.69 inches
                // At 300 DPI: 2480×3508 pixels
                int a4Width300dpi = 2480;
                int a4Height300dpi = 3508;

                // Force layout at proper A4 proportions (using 96 DPI base)
                double a4WidthAt96dpi = 794;  // 8.27 inches × 96 DPI
                double a4HeightAt96dpi = 1123; // 11.69 inches × 96 DPI

                visual.Measure(new Size(a4WidthAt96dpi, a4HeightAt96dpi));
                visual.Arrange(new Rect(0, 0, a4WidthAt96dpi, a4HeightAt96dpi));
                visual.UpdateLayout();

                // Create high-resolution bitmap at 300 DPI
                var renderBitmap = new RenderTargetBitmap(
                    a4Width300dpi, a4Height300dpi,
                    300, 300, // 300 DPI for print quality
                    PixelFormats.Pbgra32);

                // Render with proper scaling to 300 DPI
                var drawingVisual = new DrawingVisual();
                using (var drawingContext = drawingVisual.RenderOpen())
                {
                    // White background for full A4 page
                    drawingContext.DrawRectangle(Brushes.White, null,
                        new Rect(0, 0, a4Width300dpi, a4Height300dpi));

                    // Scale from 96 DPI to 300 DPI (300/96 = 3.125)
                    double scale = 300.0 / 96.0;
                    drawingContext.PushTransform(new ScaleTransform(scale, scale));

                    var visualBrush = new VisualBrush(visual)
                    {
                        Stretch = Stretch.Fill, // Fill the entire page
                        AlignmentX = AlignmentX.Left,
                        AlignmentY = AlignmentY.Top
                    };

                    drawingContext.DrawRectangle(visualBrush, null,
                        new Rect(0, 0, a4WidthAt96dpi, a4HeightAt96dpi));
                    drawingContext.Pop();
                }

                renderBitmap.Render(drawingVisual);

                // Use PNG for lossless quality
                var encoder = new PngBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(renderBitmap));

                using (var memoryStream = new MemoryStream())
                {
                    encoder.Save(memoryStream);
                    var imageBytes = memoryStream.ToArray();

                    var image = iTextSharp.text.Image.GetInstance(imageBytes);

                    // Scale to fill the entire A4 page exactly
                    image.ScaleToFit(PageSize.A4.Width, PageSize.A4.Height);
                    image.SetAbsolutePosition(0, 0); // Position at bottom-left corner

                    document.Add(image);
                }

                document.Close();
            }
        }

        /// <summary>
        /// إنشاء نسخة محسنة من الصفحة للطباعة مع الحفاظ على البيانات والتنسيق العربي
        /// </summary>
        private FrameworkElement CreateEnhancedPrintablePage(Border originalPage)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 إنشاء نسخة محسنة من الصفحة للطباعة...");

                // إنشاء حاوي للطباعة بحجم A4 محسن
                var printContainer = new Grid
                {
                    Width = 816,  // عرض A4 محسن (8.5" × 96 DPI)
                    Height = 1056, // ارتفاع A4 محسن (11" × 96 DPI)
                    Background = Brushes.White,
                    FlowDirection = FlowDirection.RightToLeft, // اتجاه عربي
                    Margin = new Thickness(0)
                };

                // إنشاء Border داخلي مع هوامش مناسبة
                var innerBorder = new Border
                {
                    Margin = new Thickness(24), // هوامش 0.25 بوصة
                    Background = Brushes.Transparent,
                    FlowDirection = FlowDirection.RightToLeft
                };

                // بدلاً من نسخ معقد، استخدم الصفحة الأصلية مباشرة مع تطبيق التحسينات
                if (originalPage.Parent is Panel parentPanel)
                {
                    parentPanel.Children.Remove(originalPage);
                }

                // تطبيق التحسينات على الصفحة الأصلية
                originalPage.FlowDirection = FlowDirection.RightToLeft;
                innerBorder.Child = originalPage;

                printContainer.Children.Add(innerBorder);

                // تحديث التخطيط بعناية
                printContainer.Measure(new Size(816, 1056));
                printContainer.Arrange(new Rect(0, 0, 816, 1056));
                printContainer.UpdateLayout();

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء نسخة محسنة من الصفحة بنجاح");
                return printContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء الصفحة المحسنة: {ex.Message}");
                return ClonePageForPrint(originalPage); // العودة للطريقة العادية
            }
        }

        /// <summary>
        /// نسخ محتوى الصفحة مع الحفاظ على البيانات والـ DataContext
        /// </summary>
        private FrameworkElement ClonePageWithDataContext(Border originalPage)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📋 نسخ محتوى الصفحة مع البيانات والـ DataContext...");

                // إنشاء Border جديد بنفس خصائص الأصلي
                var clonedBorder = new Border
                {
                    Background = originalPage.Background,
                    BorderBrush = originalPage.BorderBrush,
                    BorderThickness = originalPage.BorderThickness,
                    CornerRadius = originalPage.CornerRadius,
                    Padding = originalPage.Padding,
                    Margin = originalPage.Margin,
                    HorizontalAlignment = originalPage.HorizontalAlignment,
                    VerticalAlignment = originalPage.VerticalAlignment,
                    FlowDirection = FlowDirection.RightToLeft,
                    DataContext = originalPage.DataContext // نسخ الـ DataContext
                };

                // نسخ المحتوى الداخلي مع الـ DataContext
                if (originalPage.Child != null && originalPage.Child is FrameworkElement childElement)
                {
                    var clonedContent = CloneElementWithDataContext(childElement);
                    clonedBorder.Child = clonedContent;
                }

                System.Diagnostics.Debug.WriteLine("✅ تم نسخ محتوى الصفحة مع الـ DataContext بنجاح");
                return clonedBorder;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في نسخ محتوى الصفحة مع الـ DataContext: {ex.Message}");
                return originalPage; // إرجاع الأصلي في حالة الخطأ
            }
        }

        /// <summary>
        /// نسخ محتوى الصفحة مع الحفاظ على البيانات
        /// </summary>
        private FrameworkElement ClonePageWithData(Border originalPage)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📋 نسخ محتوى الصفحة مع البيانات...");

                // إنشاء Border جديد بنفس خصائص الأصلي
                var clonedBorder = new Border
                {
                    Background = originalPage.Background,
                    BorderBrush = originalPage.BorderBrush,
                    BorderThickness = originalPage.BorderThickness,
                    CornerRadius = originalPage.CornerRadius,
                    Padding = originalPage.Padding,
                    Margin = originalPage.Margin,
                    HorizontalAlignment = originalPage.HorizontalAlignment,
                    VerticalAlignment = originalPage.VerticalAlignment,
                    FlowDirection = FlowDirection.RightToLeft
                };

                // نسخ المحتوى الداخلي
                if (originalPage.Child != null && originalPage.Child is FrameworkElement childElement)
                {
                    var clonedContent = CloneElement(childElement);
                    clonedBorder.Child = clonedContent;
                }

                System.Diagnostics.Debug.WriteLine("✅ تم نسخ محتوى الصفحة بنجاح");
                return clonedBorder;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في نسخ محتوى الصفحة: {ex.Message}");
                return originalPage; // إرجاع الأصلي في حالة الخطأ
            }
        }

        /// <summary>
        /// نسخ عنصر UI مع الحفاظ على البيانات والتنسيق والـ DataContext
        /// </summary>
        private FrameworkElement CloneElementWithDataContext(FrameworkElement original)
        {
            try
            {
                // التعامل مع أنواع مختلفة من العناصر مع نسخ الـ DataContext
                switch (original)
                {
                    case Grid grid:
                        return CloneGridWithDataContext(grid);
                    case StackPanel stackPanel:
                        return CloneStackPanelWithDataContext(stackPanel);
                    case TextBlock textBlock:
                        return CloneTextBlockWithDataContext(textBlock);
                    case Border border:
                        return CloneBorderWithDataContext(border);
                    default:
                        // للعناصر الأخرى، استخدم الطريقة العامة مع نسخ الـ DataContext
                        var cloned = ClonePageForPrint(original as Border) ?? original;
                        cloned.DataContext = original.DataContext;
                        return cloned;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في نسخ العنصر مع الـ DataContext: {ex.Message}");
                return original;
            }
        }

        /// <summary>
        /// نسخ عنصر UI مع الحفاظ على البيانات والتنسيق
        /// </summary>
        private FrameworkElement CloneElement(FrameworkElement original)
        {
            try
            {
                // التعامل مع أنواع مختلفة من العناصر
                switch (original)
                {
                    case Grid grid:
                        return CloneGrid(grid);
                    case StackPanel stackPanel:
                        return CloneStackPanel(stackPanel);
                    case TextBlock textBlock:
                        return CloneTextBlock(textBlock);
                    case Border border:
                        return CloneBorder(border);
                    default:
                        // للعناصر الأخرى، استخدم الطريقة العامة
                        return ClonePageForPrint(original as Border) ?? original;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في نسخ العنصر: {ex.Message}");
                return original;
            }
        }

        private Grid CloneGrid(Grid original)
        {
            var cloned = new Grid
            {
                Background = original.Background,
                Margin = original.Margin,
                HorizontalAlignment = original.HorizontalAlignment,
                VerticalAlignment = original.VerticalAlignment,
                FlowDirection = FlowDirection.RightToLeft
            };

            // نسخ تعريفات الصفوف والأعمدة
            foreach (var rowDef in original.RowDefinitions)
            {
                cloned.RowDefinitions.Add(new RowDefinition { Height = rowDef.Height });
            }
            foreach (var colDef in original.ColumnDefinitions)
            {
                cloned.ColumnDefinitions.Add(new ColumnDefinition { Width = colDef.Width });
            }

            // نسخ العناصر الفرعية
            foreach (FrameworkElement child in original.Children)
            {
                var clonedChild = CloneElement(child);
                Grid.SetRow(clonedChild, Grid.GetRow(child));
                Grid.SetColumn(clonedChild, Grid.GetColumn(child));
                Grid.SetRowSpan(clonedChild, Grid.GetRowSpan(child));
                Grid.SetColumnSpan(clonedChild, Grid.GetColumnSpan(child));
                cloned.Children.Add(clonedChild);
            }

            return cloned;
        }

        private StackPanel CloneStackPanel(StackPanel original)
        {
            var cloned = new StackPanel
            {
                Background = original.Background,
                Margin = original.Margin,
                Orientation = original.Orientation,
                HorizontalAlignment = original.HorizontalAlignment,
                VerticalAlignment = original.VerticalAlignment,
                FlowDirection = FlowDirection.RightToLeft
            };

            foreach (FrameworkElement child in original.Children)
            {
                cloned.Children.Add(CloneElement(child));
            }

            return cloned;
        }

        private TextBlock CloneTextBlock(TextBlock original)
        {
            return new TextBlock
            {
                Text = original.Text,
                FontFamily = original.FontFamily,
                FontSize = original.FontSize,
                FontWeight = original.FontWeight,
                Foreground = original.Foreground,
                Background = original.Background,
                Margin = original.Margin,
                Padding = original.Padding,
                HorizontalAlignment = original.HorizontalAlignment,
                VerticalAlignment = original.VerticalAlignment,
                TextAlignment = original.TextAlignment,
                TextWrapping = original.TextWrapping,
                FlowDirection = FlowDirection.RightToLeft
            };
        }

        private Border CloneBorder(Border original)
        {
            var cloned = new Border
            {
                Background = original.Background,
                BorderBrush = original.BorderBrush,
                BorderThickness = original.BorderThickness,
                CornerRadius = original.CornerRadius,
                Padding = original.Padding,
                Margin = original.Margin,
                HorizontalAlignment = original.HorizontalAlignment,
                VerticalAlignment = original.VerticalAlignment,
                FlowDirection = FlowDirection.RightToLeft
            };

            if (original.Child != null && original.Child is FrameworkElement childElement)
            {
                cloned.Child = CloneElement(childElement);
            }

            return cloned;
        }

        // دوال النسخ مع الـ DataContext
        private Grid CloneGridWithDataContext(Grid original)
        {
            var cloned = new Grid
            {
                Background = original.Background,
                Margin = original.Margin,
                HorizontalAlignment = original.HorizontalAlignment,
                VerticalAlignment = original.VerticalAlignment,
                FlowDirection = FlowDirection.RightToLeft,
                DataContext = original.DataContext // نسخ الـ DataContext
            };

            // نسخ تعريفات الصفوف والأعمدة
            foreach (var rowDef in original.RowDefinitions)
            {
                cloned.RowDefinitions.Add(new RowDefinition { Height = rowDef.Height });
            }
            foreach (var colDef in original.ColumnDefinitions)
            {
                cloned.ColumnDefinitions.Add(new ColumnDefinition { Width = colDef.Width });
            }

            // نسخ العناصر الفرعية مع الـ DataContext
            foreach (FrameworkElement child in original.Children)
            {
                var clonedChild = CloneElementWithDataContext(child);
                Grid.SetRow(clonedChild, Grid.GetRow(child));
                Grid.SetColumn(clonedChild, Grid.GetColumn(child));
                Grid.SetRowSpan(clonedChild, Grid.GetRowSpan(child));
                Grid.SetColumnSpan(clonedChild, Grid.GetColumnSpan(child));
                cloned.Children.Add(clonedChild);
            }

            return cloned;
        }

        private StackPanel CloneStackPanelWithDataContext(StackPanel original)
        {
            var cloned = new StackPanel
            {
                Background = original.Background,
                Margin = original.Margin,
                Orientation = original.Orientation,
                HorizontalAlignment = original.HorizontalAlignment,
                VerticalAlignment = original.VerticalAlignment,
                FlowDirection = FlowDirection.RightToLeft,
                DataContext = original.DataContext // نسخ الـ DataContext
            };

            foreach (FrameworkElement child in original.Children)
            {
                cloned.Children.Add(CloneElementWithDataContext(child));
            }

            return cloned;
        }

        private TextBlock CloneTextBlockWithDataContext(TextBlock original)
        {
            return new TextBlock
            {
                Text = original.Text,
                FontFamily = original.FontFamily,
                FontSize = original.FontSize,
                FontWeight = original.FontWeight,
                Foreground = original.Foreground,
                Background = original.Background,
                Margin = original.Margin,
                Padding = original.Padding,
                HorizontalAlignment = original.HorizontalAlignment,
                VerticalAlignment = original.VerticalAlignment,
                TextAlignment = original.TextAlignment,
                TextWrapping = original.TextWrapping,
                FlowDirection = FlowDirection.RightToLeft,
                DataContext = original.DataContext // نسخ الـ DataContext
            };
        }

        private Border CloneBorderWithDataContext(Border original)
        {
            var cloned = new Border
            {
                Background = original.Background,
                BorderBrush = original.BorderBrush,
                BorderThickness = original.BorderThickness,
                CornerRadius = original.CornerRadius,
                Padding = original.Padding,
                Margin = original.Margin,
                HorizontalAlignment = original.HorizontalAlignment,
                VerticalAlignment = original.VerticalAlignment,
                FlowDirection = FlowDirection.RightToLeft,
                DataContext = original.DataContext // نسخ الـ DataContext
            };

            if (original.Child != null && original.Child is FrameworkElement childElement)
            {
                cloned.Child = CloneElementWithDataContext(childElement);
            }

            return cloned;
        }
    }
}
