<UserControl x:Class="DriverManagementSystem.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DriverManagementSystem.Views"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/Colors.xaml"/>
                <ResourceDictionary Source="../Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="20">
        <StackPanel>
            
            <!-- Page Header -->
            <Grid Margin="0,0,0,30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Orientation="Horizontal" Grid.Column="0">
                    <TextBlock Text="🏠"
                             FontSize="32"
                             Foreground="{StaticResource PrimaryBrush}"
                             VerticalAlignment="Center"
                             Margin="0,0,15,0"/>
                    <TextBlock Text="لوحة التحكم - الوصول السريع"
                             FontSize="28"
                             FontWeight="Bold"
                             Foreground="{StaticResource TextPrimaryBrush}"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <!-- تم إزالة أزرار "إضافة البيانات" و "تحديث النظام" -->
            </Grid>

            <!-- Statistics Section -->
            <Border Style="{StaticResource CardStyle}" Margin="0,0,0,30">
                <StackPanel>
                    <TextBlock Text="📊 إحصائيات النظام"
                             FontSize="24"
                             FontWeight="Bold"
                             Foreground="{StaticResource TextPrimaryBrush}"
                             Margin="0,0,0,30"
                             HorizontalAlignment="Center"/>

                    <ItemsControl ItemsSource="{Binding Statistics}">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <UniformGrid Columns="5" Rows="1"/>
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Background="White"
                                       CornerRadius="12"
                                       Margin="10"
                                       Height="120"
                                       BorderBrush="{StaticResource PrimaryBrush}"
                                       BorderThickness="2">
                                    <Border.Effect>
                                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="5" Opacity="0.3" BlurRadius="10"/>
                                    </Border.Effect>
                                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                                        <Border Background="{StaticResource PrimaryBrush}"
                                               CornerRadius="20"
                                               Width="40" Height="40"
                                               Margin="0,0,0,10">
                                            <TextBlock Text="{Binding Icon}"
                                                     FontSize="20"
                                                     HorizontalAlignment="Center"
                                                     VerticalAlignment="Center"
                                                     Foreground="White"/>
                                        </Border>
                                        <TextBlock Text="{Binding Count}"
                                                 FontSize="24"
                                                 FontWeight="Bold"
                                                 HorizontalAlignment="Center"
                                                 Foreground="{StaticResource PrimaryBrush}"/>
                                        <TextBlock Text="{Binding Title}"
                                                 FontSize="12"
                                                 FontWeight="SemiBold"
                                                 HorizontalAlignment="Center"
                                                 Foreground="#666"
                                                 TextWrapping="Wrap"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </StackPanel>
            </Border>

            <!-- Professional Templates Status -->

            <!-- Quick Actions -->
            <Border Background="White"
                    CornerRadius="25"
                    Margin="0,30,0,0"
                    Padding="0"
                    BorderBrush="#E0E0E0"
                    BorderThickness="1">
                <Border.Effect>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="8" Opacity="0.15" BlurRadius="20"/>
                </Border.Effect>

                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Modern Header -->
                    <Border Grid.Row="0"
                            CornerRadius="25,25,0,0"
                            Padding="30,20">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                <GradientStop Color="#667eea" Offset="0"/>
                                <GradientStop Color="#764ba2" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <Border Background="White"
                                    CornerRadius="15"
                                    Width="40" Height="40"
                                    Margin="0,0,15,0">
                                <TextBlock Text="🚀"
                                         FontSize="22"
                                         HorizontalAlignment="Center"
                                         VerticalAlignment="Center"/>
                            </Border>
                            <TextBlock Text="الوصول السريع"
                                     FontSize="28"
                                     FontWeight="Bold"
                                     Foreground="White"
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- Actions Container -->
                    <Grid Grid.Row="1" Margin="40,40,40,30">
                        <ItemsControl ItemsSource="{Binding QuickActions}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <UniformGrid Columns="2" Rows="2"/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="White"
                                           CornerRadius="20"
                                           Margin="15"
                                           Height="140"
                                           BorderBrush="#F0F0F0"
                                           BorderThickness="2">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="6" Opacity="0.3" BlurRadius="15"/>
                                        </Border.Effect>
                                        <Button Background="Transparent"
                                              BorderThickness="0"
                                              Cursor="Hand"
                                              Command="{Binding DataContext.QuickActionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding Action}">
                                            <Button.Style>
                                                <Style TargetType="Button">
                                                    <Setter Property="Template">
                                                        <Setter.Value>
                                                            <ControlTemplate TargetType="Button">
                                                                <Border Background="{TemplateBinding Background}"
                                                                       CornerRadius="20"
                                                                       Padding="20">
                                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                                    <Border.Style>
                                                                        <Style TargetType="Border">
                                                                            <Style.Triggers>
                                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                                    <Setter Property="Background">
                                                                                        <Setter.Value>
                                                                                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                                                                <GradientStop Color="#F8F9FA" Offset="0"/>
                                                                                                <GradientStop Color="#E9ECEF" Offset="1"/>
                                                                                            </LinearGradientBrush>
                                                                                        </Setter.Value>
                                                                                    </Setter>
                                                                                    <Setter Property="Effect">
                                                                                        <Setter.Value>
                                                                                            <DropShadowEffect Color="#667eea" Direction="270" ShadowDepth="10" Opacity="0.4" BlurRadius="20"/>
                                                                                        </Setter.Value>
                                                                                    </Setter>
                                                                                </Trigger>
                                                                            </Style.Triggers>
                                                                        </Style>
                                                                    </Border.Style>
                                                                </Border>
                                                            </ControlTemplate>
                                                        </Setter.Value>
                                                    </Setter>
                                                </Style>
                                            </Button.Style>
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                <!-- Icon -->
                                                <Border Background="#667eea"
                                                       CornerRadius="15"
                                                       Width="50" Height="50"
                                                       Margin="0,0,15,0">
                                                    <TextBlock Text="{Binding Icon}"
                                                             FontSize="28"
                                                             HorizontalAlignment="Center"
                                                             VerticalAlignment="Center"
                                                             Foreground="White"
                                                             FontWeight="Bold"/>
                                                </Border>
                                                <!-- Title -->
                                                <TextBlock Text="{Binding Title}"
                                                         FontSize="16"
                                                         FontWeight="SemiBold"
                                                         VerticalAlignment="Center"
                                                         TextWrapping="Wrap"
                                                         Foreground="#2C3E50"
                                                         MaxWidth="120"/>
                                            </StackPanel>
                                        </Button>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </Grid>
                </Grid>
            </Border>

        </StackPanel>
    </ScrollViewer>
</UserControl>
