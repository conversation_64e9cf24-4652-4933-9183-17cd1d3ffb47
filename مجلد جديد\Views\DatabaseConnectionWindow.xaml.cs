using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Data;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// واجهة إعداد اتصال قاعدة البيانات الاحترافية
    /// </summary>
    public partial class DatabaseConnectionWindow : Window, INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler? PropertyChanged;

        private ObservableCollection<TableInfo> _tables = new();
        public ObservableCollection<TableInfo> Tables
        {
            get => _tables;
            set
            {
                _tables = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(Tables)));
            }
        }

        public bool ConnectionSuccessful { get; private set; }
        public string ConnectionString { get; private set; } = string.Empty;

        public DatabaseConnectionWindow()
        {
            InitializeComponent();
            TablesDataGrid.ItemsSource = Tables;
            LoadCurrentSettings();
            SetupEventHandlers();
        }

        private void LoadCurrentSettings()
        {
            try
            {
                // تحميل الإعدادات الحالية
                ServerNameTextBox.Text = Environment.GetEnvironmentVariable("SQL_SERVER_NAME") ?? "localhost";
                DatabaseNameTextBox.Text = Environment.GetEnvironmentVariable("SQL_DATABASE_NAME") ?? "SFDSYS";
                
                var useWindowsAuth = Environment.GetEnvironmentVariable("SQL_USE_WINDOWS_AUTH");
                if (useWindowsAuth?.ToLower() == "false")
                {
                    SqlAuthRadio.IsChecked = true;
                    UsernameTextBox.Text = Environment.GetEnvironmentVariable("SQL_USERNAME") ?? "";
                }
                else
                {
                    WindowsAuthRadio.IsChecked = true;
                }
            }
            catch (Exception ex)
            {
                UpdateConnectionStatus($"❌ خطأ في تحميل الإعدادات: {ex.Message}", false);
            }
        }

        private void SetupEventHandlers()
        {
            WindowsAuthRadio.Checked += (s, e) => ToggleAuthenticationFields(false);
            SqlAuthRadio.Checked += (s, e) => ToggleAuthenticationFields(true);
        }

        private void ToggleAuthenticationFields(bool enableSqlAuth)
        {
            UsernameTextBox.IsEnabled = enableSqlAuth;
            PasswordBox.IsEnabled = enableSqlAuth;
            UsernameLabel.Opacity = enableSqlAuth ? 1.0 : 0.5;
            PasswordLabel.Opacity = enableSqlAuth ? 1.0 : 0.5;
        }

        private async void TestConnectionButton_Click(object sender, RoutedEventArgs e)
        {
            await TestConnection();
        }

        private async Task TestConnection()
        {
            try
            {
                UpdateConnectionStatus("⏳ جاري اختبار الاتصال...", null);
                ConnectionProgressBar.Visibility = Visibility.Visible;
                ConnectionProgressBar.IsIndeterminate = true;

                var connectionString = BuildConnectionString();
                
                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();
                
                // اختبار إنشاء قاعدة البيانات إذا لم تكن موجودة
                await EnsureDatabaseExists(connection);
                
                ConnectionString = connectionString;
                ConnectionSuccessful = true;
                
                UpdateConnectionStatus("✅ تم الاتصال بنجاح!", true);
                
                // تحديث قائمة الجداول
                await RefreshTables();
            }
            catch (Exception ex)
            {
                ConnectionSuccessful = false;
                UpdateConnectionStatus($"❌ فشل الاتصال: {ex.Message}", false);
            }
            finally
            {
                ConnectionProgressBar.Visibility = Visibility.Collapsed;
                ConnectionProgressBar.IsIndeterminate = false;
            }
        }

        private string BuildConnectionString()
        {
            var builder = new SqlConnectionStringBuilder();
            
            builder.DataSource = ServerNameTextBox.Text.Trim();
            builder.InitialCatalog = DatabaseNameTextBox.Text.Trim();
            builder.ConnectTimeout = 30;
            builder.CommandTimeout = 60;
            builder.TrustServerCertificate = true;

            if (WindowsAuthRadio.IsChecked == true)
            {
                builder.IntegratedSecurity = true;
            }
            else
            {
                builder.UserID = UsernameTextBox.Text.Trim();
                builder.Password = PasswordBox.Password;
            }

            return builder.ConnectionString;
        }

        private async Task EnsureDatabaseExists(SqlConnection connection)
        {
            var databaseName = DatabaseNameTextBox.Text.Trim();
            
            // التحقق من وجود قاعدة البيانات
            var checkDbQuery = $"SELECT COUNT(*) FROM sys.databases WHERE name = '{databaseName}'";
            using var checkCmd = new SqlCommand(checkDbQuery, connection);
            var dbExists = (int)await checkCmd.ExecuteScalarAsync() > 0;

            if (!dbExists)
            {
                // إنشاء قاعدة البيانات
                var createDbQuery = $"CREATE DATABASE [{databaseName}]";
                using var createCmd = new SqlCommand(createDbQuery, connection);
                await createCmd.ExecuteNonQueryAsync();
                
                UpdateConnectionStatus($"✅ تم إنشاء قاعدة البيانات '{databaseName}' بنجاح!", true);
            }
        }

        private async void RefreshTablesButton_Click(object sender, RoutedEventArgs e)
        {
            await RefreshTables();
        }

        private async Task RefreshTables()
        {
            if (!ConnectionSuccessful)
            {
                MessageBox.Show("يرجى اختبار الاتصال أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                Tables.Clear();
                TablesCountText.Text = "⏳ جاري تحديث القائمة...";

                using var connection = new SqlConnection(ConnectionString);
                await connection.OpenAsync();

                // الحصول على قائمة الجداول
                var tablesQuery = @"
                    SELECT
                        TABLE_NAME as TableName,
                        0 as RowCount
                    FROM INFORMATION_SCHEMA.TABLES
                    WHERE TABLE_TYPE = 'BASE TABLE'
                    ORDER BY TABLE_NAME";

                using var cmd = new SqlCommand(tablesQuery, connection);
                using var reader = await cmd.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    var tableName = reader.GetString("TableName");

                    // حساب عدد الصفوف لكل جدول
                    long rowCount = 0;
                    try
                    {
                        using var countCmd = new SqlCommand($"SELECT COUNT(*) FROM [{tableName}]", connection);
                        var result = await countCmd.ExecuteScalarAsync();
                        rowCount = Convert.ToInt64(result);
                    }
                    catch
                    {
                        rowCount = 0; // في حالة وجود خطأ
                    }

                    Tables.Add(new TableInfo
                    {
                        TableName = tableName,
                        RowCount = rowCount,
                        Status = rowCount > 0 ? "يحتوي على بيانات" : "فارغ"
                    });
                }

                TablesCountText.Text = $"عدد الجداول: {Tables.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث قائمة الجداول: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                TablesCountText.Text = "خطأ في التحديث";
            }
        }

        private async void CreateDatabaseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateConnectionStatus("⏳ جاري إنشاء الجداول...", null);
                
                var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                    .UseSqlServer(ConnectionString)
                    .Options;

                using var context = new ApplicationDbContext(options);
                
                // إنشاء الجداول
                await context.Database.EnsureCreatedAsync();
                
                UpdateConnectionStatus("✅ تم إنشاء جميع الجداول بنجاح!", true);
                
                // تحديث قائمة الجداول
                await RefreshTables();
                
                MessageBox.Show("تم إنشاء جميع الجداول بنجاح!", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                UpdateConnectionStatus($"❌ فشل إنشاء الجداول: {ex.Message}", false);
                MessageBox.Show($"خطأ في إنشاء الجداول: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ConnectionSuccessful)
            {
                MessageBox.Show("يرجى اختبار الاتصال أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                // حفظ الإعدادات في متغيرات البيئة
                Environment.SetEnvironmentVariable("SQL_SERVER_NAME", ServerNameTextBox.Text.Trim());
                Environment.SetEnvironmentVariable("SQL_DATABASE_NAME", DatabaseNameTextBox.Text.Trim());
                Environment.SetEnvironmentVariable("SQL_USE_WINDOWS_AUTH", (WindowsAuthRadio.IsChecked == true).ToString());
                
                if (SqlAuthRadio.IsChecked == true)
                {
                    Environment.SetEnvironmentVariable("SQL_USERNAME", UsernameTextBox.Text.Trim());
                    // ملاحظة: كلمة المرور لا يتم حفظها لأسباب أمنية
                }

                MessageBox.Show("تم حفظ الإعدادات بنجاح!", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void UpdateConnectionStatus(string message, bool? success)
        {
            ConnectionStatusText.Text = message;
            
            if (success.HasValue)
            {
                ConnectionStatusText.Foreground = success.Value ? 
                    System.Windows.Media.Brushes.Green : 
                    System.Windows.Media.Brushes.Red;
            }
            else
            {
                ConnectionStatusText.Foreground = System.Windows.Media.Brushes.Orange;
            }
        }
    }

    /// <summary>
    /// معلومات الجدول
    /// </summary>
    public class TableInfo
    {
        public string TableName { get; set; } = string.Empty;
        public long RowCount { get; set; }
        public string Status { get; set; } = string.Empty;
    }
}
