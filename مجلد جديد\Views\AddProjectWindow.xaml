<Window x:Class="DriverManagementSystem.Views.AddProjectWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة المشاريع"
        Height="700" Width="1200"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="White">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>

        <!-- Clean Professional TextBox Style -->
        <Style x:Key="CleanTextBox" TargetType="TextBox">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="#333"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#007ACC"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </Trigger>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="BorderBrush" Value="#999"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Clean Button Style -->
        <Style x:Key="CleanButton" TargetType="Button">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="5"
                               Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Padding="25">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="#007ACC" Offset="0"/>
                    <GradientStop Color="#005A9B" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <StackPanel Orientation="Horizontal">
                <Border Background="White" CornerRadius="25" Width="50" Height="50" Margin="0,0,20,0">
                    <Border.Effect>
                        <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="5"/>
                    </Border.Effect>
                    <TextBlock Text="📋" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <StackPanel VerticalAlignment="Center">
                    <TextBlock Text="إدارة المشاريع" FontSize="24" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="عرض وإدارة جميع المشاريع - إضافة وتعديل وحذف المشاريع" FontSize="14" Foreground="#B3D9FF"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="400"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Left Panel - Add/Edit Form -->
            <Border Grid.Column="0" Background="#F8F9FA" CornerRadius="10" Padding="20" BorderBrush="#E0E0E0" BorderThickness="1" Margin="0,0,10,-83">
                <StackPanel>
                    <TextBlock Text="📝 إضافة/تعديل مشروع" FontWeight="Bold" FontSize="16"
                              Foreground="#007ACC" Margin="0,0,0,20" HorizontalAlignment="Center"/>

                    <!-- Project Number -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="📋 رقم المشروع *" FontWeight="Bold" FontSize="14"
                                  Foreground="#007ACC" Margin="0,0,0,8"/>
                        <TextBox x:Name="ProjectNumberTextBox"
                                Style="{StaticResource CleanTextBox}"
                                Text="{Binding ProjectNumber, UpdateSourceTrigger=PropertyChanged}"
                                Height="53"
                                MaxLength="50"
                                ToolTip="أدخل رقم المشروع (مثال: 911-13727)"/>
                        <TextBlock Text="{Binding ProjectNumberError}"
                                  Foreground="Red" FontSize="11" Margin="0,5,0,0"
                                  Visibility="{Binding HasProjectNumberError, Converter={StaticResource BoolToVisibilityConverter}}"/>
                    </StackPanel>

                    <!-- Project Name -->
                    <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="📝 اسم المشروع *" FontWeight="Bold" FontSize="14"
                                  Foreground="#007ACC" Margin="0,0,0,8"/>
                        <TextBox x:Name="ProjectNameTextBox"
                                Style="{StaticResource CleanTextBox}"
                                Text="{Binding ProjectName, UpdateSourceTrigger=PropertyChanged}"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                Height="120"
                                VerticalContentAlignment="Top"
                                VerticalScrollBarVisibility="Auto"
                                MaxLength="500"
                                ToolTip="أدخل اسم المشروع كاملاً"/>
                        <TextBlock Text="{Binding ProjectNameError}"
                                  Foreground="Red" FontSize="11" Margin="0,5,0,0"
                                  Visibility="{Binding HasProjectNameError, Converter={StaticResource BoolToVisibilityConverter}}"/>
                    </StackPanel>

                    <!-- Action Buttons -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                        <!-- Save Button -->
                        <Button Content="💾 حفظ"
                               Background="#4CAF50" Foreground="White"
                               Style="{StaticResource CleanButton}"
                               Command="{Binding SaveProjectCommand}"
                               IsEnabled="{Binding CanSave}"
                               Width="100" Height="40"
                               Margin="0,0,10,0"/>

                        <!-- Update Button -->
                        <Button Content="✏️ تحديث"
                               Background="#FF9800" Foreground="White"
                               Style="{StaticResource CleanButton}"
                               Command="{Binding UpdateProjectCommand}"
                               IsEnabled="{Binding CanUpdate}"
                               Width="100" Height="40"
                               Margin="10,0,10,0"/>

                        <!-- Clear Button -->
                        <Button Content="🗑️ مسح"
                               Background="#9E9E9E" Foreground="White"
                               Style="{StaticResource CleanButton}"
                               Command="{Binding ClearFormCommand}"
                               Width="100" Height="40"
                               Margin="10,0,0,0"/>
                    </StackPanel>

                    <!-- Info Message -->
                    <Border Background="#E8F5E8" CornerRadius="8" Padding="15" BorderBrush="#4CAF50" BorderThickness="1" Margin="0,20,0,0">
                        <StackPanel>
                            <TextBlock Text="💡 معلومات مفيدة:" FontWeight="Bold" FontSize="12" Foreground="#2E7D32" Margin="0,0,0,5"/>
                            <TextBlock Text="• انقر على مشروع لتعديله" FontSize="11" Foreground="#2E7D32"/>
                            <TextBlock Text="• سيتم التحقق من التكرار تلقائياً" FontSize="11" Foreground="#2E7D32"/>
                            <TextBlock Text="• يمكن حذف المشاريع من الجدول" FontSize="11" Foreground="#2E7D32"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Border>

            <!-- Right Panel - Projects List -->
            <Border Grid.Column="1" Background="White" CornerRadius="10" Padding="20" BorderBrush="#E0E0E0" BorderThickness="1" Margin="10,0,0,-30">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Projects Header -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                        <TextBlock Text="📊 قائمة المشاريع" FontWeight="Bold" FontSize="16"
                                  Foreground="#007ACC" VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding ProjectsCount, StringFormat='({0} مشروع)'}"
                                  FontSize="14" Foreground="#666" VerticalAlignment="Center" Margin="10,0,0,0"/>
                    </StackPanel>

                    <!-- Search Box -->
                    <Border Grid.Row="1" Background="#F8F9FA" CornerRadius="8" Padding="10" BorderBrush="#E0E0E0" BorderThickness="1" Margin="0,0,0,15">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔍" FontSize="16" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <TextBox x:Name="SearchTextBox"
                                    Style="{StaticResource CleanTextBox}"
                                    Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                    Width="300" Height="41"
                                    ToolTip="البحث في المشاريع..."
                                    VerticalAlignment="Center"/>
                            <Button Content="🔄 تحديث"
                                   Background="#2196F3" Foreground="White"
                                   Style="{StaticResource CleanButton}"
                                   Command="{Binding RefreshProjectsCommand}"
                                   Width="96" Height="35"
                                   Margin="10,0,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Projects DataGrid -->
                    <DataGrid Grid.Row="2"
                             x:Name="ProjectsDataGrid"
                             ItemsSource="{Binding FilteredProjects}"
                             SelectedItem="{Binding SelectedProject}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             SelectionMode="Single"
                             Background="White"
                             BorderBrush="#E0E0E0"
                             BorderThickness="1"
                             FontSize="12">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="60" IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#007ACC"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="8"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="رقم المشروع" Binding="{Binding ProjectNumber}" Width="150" IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#007ACC"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="8"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="اسم المشروع" Binding="{Binding ProjectName}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#007ACC"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="8"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="TextWrapping" Value="Wrap"/>
                                        <Setter Property="Padding" Value="5"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTemplateColumn Header="الإجراءات" Width="120">
                                <DataGridTemplateColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#007ACC"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="8"/>
                                    </Style>
                                </DataGridTemplateColumn.HeaderStyle>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Content="✏️"
                                                   Background="#FF9800" Foreground="White"
                                                   Style="{StaticResource CleanButton}"
                                                   Command="{Binding DataContext.EditProjectCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                   CommandParameter="{Binding}"
                                                   Width="30" Height="25"
                                                   Margin="2"
                                                   ToolTip="تعديل"/>
                                            <Button Content="🗑️"
                                                   Background="#F44336" Foreground="White"
                                                   Style="{StaticResource CleanButton}"
                                                   Command="{Binding DataContext.DeleteProjectCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                   CommandParameter="{Binding}"
                                                   Width="30" Height="25"
                                                   Margin="2"
                                                   ToolTip="حذف"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>

                        <DataGrid.RowStyle>
                            <Style TargetType="DataGridRow">
                                <Setter Property="Background" Value="White"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#E3F2FD"/>
                                    </Trigger>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="#BBDEFB"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </DataGrid.RowStyle>
                    </DataGrid>
                </Grid>
            </Border>
        </Grid>

        <!-- Footer Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,15">
            <Button Content="❌ إغلاق"
                   Background="#F44336" Foreground="White"
                   Style="{StaticResource CleanButton}"
                   Command="{Binding CancelCommand}"
                   Width="120" Height="40"/>
        </StackPanel>

    </Grid>
</Window>
