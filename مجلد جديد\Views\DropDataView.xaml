<UserControl x:Class="DriverManagementSystem.Views.DropDataView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DriverManagementSystem.Views"
             xmlns:converters="clr-namespace:DriverManagementSystem.Converters"
             xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/Colors.xaml"/>
                <ResourceDictionary Source="../Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
            <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Professional Header -->
        <Border Grid.Row="0" CornerRadius="0,0,15,15" Margin="0,0,0,20">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#1565C0" Offset="0"/>
                    <GradientStop Color="#0D47A1" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="5" Opacity="0.3" BlurRadius="10"/>
            </Border.Effect>

            <Grid Margin="30,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Logo and Title -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="White" CornerRadius="25" Width="50" Height="50" Margin="0,0,15,0">
                        <TextBlock Text="🗂️" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="إدارة بيانات النزول الميداني"
                                  FontSize="26" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Text="نظام احترافي متطور لإدارة الزيارات الميدانية"
                                  FontSize="14" Foreground="#E3F2FD" Margin="0,5,0,0"/>
                    </StackPanel>
                </StackPanel>

                <!-- Current Date and Time -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBlock x:Name="CurrentDateText" Text="{Binding CurrentDate}"
                              FontSize="16" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="CurrentTimeText" Text="{Binding CurrentTime}"
                              FontSize="14" Foreground="#E3F2FD" HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Status -->
                <StackPanel Grid.Column="2" VerticalAlignment="Center">
                    <TextBlock Text="📊 حالة النظام" FontSize="14" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="{Binding StatusMessage}" FontSize="12" Foreground="#81C784"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="10">
            <StackPanel>

                <!-- Professional Basic Information Section -->
                <Border CornerRadius="12" Margin="10" Padding="25">
                    <Border.Background>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                            <GradientStop Color="White" Offset="0"/>
                            <GradientStop Color="#F8F9FA" Offset="1"/>
                        </LinearGradientBrush>
                    </Border.Background>
                    <Border.Effect>
                        <DropShadowEffect Color="#1976D2" Direction="270" ShadowDepth="5" Opacity="0.2" BlurRadius="15"/>
                    </Border.Effect>

                    <StackPanel>
                        <!-- Section Header -->
                        <Border Background="#1976D2" CornerRadius="8" Padding="15,10" Margin="0,0,0,25">
                            <StackPanel Orientation="Horizontal">
                                <Border Background="White" CornerRadius="15" Width="30" Height="30" Margin="0,0,15,0">
                                    <TextBlock Text="📋" FontSize="16" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="المعلومات الأساسية للزيارة الميدانية"
                                          FontSize="20" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="1*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Row 1 - رقم الإدخال -->
                            <Border Grid.Row="0" Grid.Column="0" Background="#F3E5F5" CornerRadius="8" Margin="8" Padding="15">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                        <TextBlock Text="🔢" FontSize="14" Margin="0,0,8,0"/>
                                        <TextBlock Text="رقم إدخال" FontWeight="Bold" FontSize="14"/>
                                    </StackPanel>
                                    <TextBox Text="{Binding NumericInput, UpdateSourceTrigger=PropertyChanged}"
                                            Height="40" FontSize="14" Background="White" FontWeight="Bold"
                                            BorderBrush="#9C27B0" BorderThickness="2"
                                            HorizontalContentAlignment="Center" VerticalContentAlignment="Center"
                                            MaxLength="10"/>
                                </StackPanel>
                            </Border>

                            <!-- وقت وتاريخ الإرسال -->
                            <Border Grid.Row="0" Grid.Column="1" Background="#E8F5E8" CornerRadius="8" Margin="8" Padding="15">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                        <TextBlock Text="⏰" FontSize="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="وقت وتاريخ الإرسال" FontWeight="Bold" FontSize="16" Foreground="#2E7D32"/>
                                    </StackPanel>
                                    <TextBox Text="{Binding FormattedSubmissionTime}" IsReadOnly="True"
                                            Height="45" FontSize="14" Background="White" FontWeight="Bold"
                                            BorderBrush="#4CAF50" BorderThickness="3"
                                            HorizontalContentAlignment="Center" VerticalContentAlignment="Center">
                                        <TextBox.Style>
                                            <Style TargetType="TextBox">
                                                <Setter Property="Template">
                                                    <Setter.Value>
                                                        <ControlTemplate TargetType="TextBox">
                                                            <Border Background="{TemplateBinding Background}"
                                                                   BorderBrush="{TemplateBinding BorderBrush}"
                                                                   BorderThickness="{TemplateBinding BorderThickness}"
                                                                   CornerRadius="8">
                                                                <ScrollViewer x:Name="PART_ContentHost" Margin="5"/>
                                                            </Border>
                                                        </ControlTemplate>
                                                    </Setter.Value>
                                                </Setter>
                                            </Style>
                                        </TextBox.Style>
                                    </TextBox>
                                </StackPanel>
                            </Border>

                            <!-- رقم الزيارة -->
                            <Border Grid.Row="0" Grid.Column="2" Background="#E3F2FD" CornerRadius="8" Margin="8" Padding="15">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                        <TextBlock Text="🔢" FontSize="14" Margin="0,0,8,0"/>
                                        <TextBlock Text="رقم الزيارة (مطلوب)" FontWeight="Bold" FontSize="14"/>
                                    </StackPanel>
                                    <Grid>
                                        <TextBox Text="{Binding VisitNumber, UpdateSourceTrigger=PropertyChanged}"
                                                Height="40" FontSize="14" Background="White" FontWeight="Bold"
                                                BorderBrush="#1976D2" BorderThickness="2"
                                                HorizontalContentAlignment="Center" VerticalContentAlignment="Center"
                                                MaxLength="20"/>
                                        <Border Background="#FFCDD2" CornerRadius="5" Padding="5,2"
                                               HorizontalAlignment="Right" VerticalAlignment="Top" Margin="5"
                                               Visibility="{Binding IsVisitNumberDuplicate, Converter={StaticResource BooleanToVisibilityConverter}}">
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="⚠️" FontSize="12" Margin="0,0,3,0"/>
                                                <TextBlock Text="مكرر!" FontSize="10" Foreground="#D32F2F" FontWeight="Bold"/>
                                            </StackPanel>
                                        </Border>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <Border Grid.Row="0" Grid.Column="3" Background="#E8F5E8" CornerRadius="8" Margin="8" Padding="15">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                        <TextBlock Text="📅" FontSize="14" Margin="0,0,8,0"/>
                                        <TextBlock Text="تاريخ الإضافة" FontWeight="Bold" FontSize="14"/>
                                    </StackPanel>
                                    <DatePicker SelectedDate="{Binding AddDate, UpdateSourceTrigger=PropertyChanged}"
                                               Height="40" FontSize="14" BorderBrush="#4CAF50" BorderThickness="2"/>
                                </StackPanel>
                            </Border>

                            <Border Grid.Row="0" Grid.Column="4" Background="#FFF3E0" CornerRadius="8" Margin="8" Padding="15">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                        <TextBlock Text="🌙" FontSize="14" Margin="0,0,8,0"/>
                                        <TextBlock Text="التاريخ الهجري (آلي)" FontWeight="Bold" FontSize="14"/>
                                    </StackPanel>
                                    <TextBox Text="{Binding HijriDate}" IsReadOnly="True"
                                            Height="40" FontSize="14" Background="White" FontWeight="Bold"
                                            BorderBrush="#FF9800" BorderThickness="2"
                                            HorizontalContentAlignment="Center" VerticalContentAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <Border Grid.Row="0" Grid.Column="4" Background="#E8EAF6" CornerRadius="8" Margin="8" Padding="15">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                        <TextBlock Text="🔐" FontSize="14" Margin="0,0,8,0"/>
                                        <TextBlock Text="عقد السائق (آلي)" FontWeight="Bold" FontSize="14"/>
                                    </StackPanel>
                                    <TextBox Text="{Binding DriverContract}" IsReadOnly="True"
                                            Height="40" FontSize="12" Background="White" FontWeight="Bold"
                                            BorderBrush="#3F51B5" BorderThickness="2"
                                            HorizontalContentAlignment="Center" VerticalContentAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <!-- Row 2 - تاريخ النزول -->
                            <Border Grid.Row="1" Grid.Column="0" Background="#E0F2F1" CornerRadius="8" Margin="8" Padding="15">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                        <TextBlock Text="🚀" FontSize="14" Margin="0,0,8,0"/>
                                        <TextBlock Text="تاريخ النزول" FontWeight="Bold" FontSize="14"/>
                                    </StackPanel>
                                    <DatePicker SelectedDate="{Binding DepartureDate, UpdateSourceTrigger=PropertyChanged}"
                                               Height="40" FontSize="14" BorderBrush="#009688" BorderThickness="2"/>
                                </StackPanel>
                            </Border>

                            <!-- تاريخ العودة -->
                            <Border Grid.Row="1" Grid.Column="1" Background="#FCE4EC" CornerRadius="8" Margin="8" Padding="15">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                        <TextBlock Text="🏠" FontSize="14" Margin="0,0,8,0"/>
                                        <TextBlock Text="تاريخ العودة" FontWeight="Bold" FontSize="14"/>
                                    </StackPanel>
                                    <DatePicker SelectedDate="{Binding ReturnDate, UpdateSourceTrigger=PropertyChanged}"
                                               Height="40" FontSize="14" BorderBrush="#E91E63" BorderThickness="2"/>
                                </StackPanel>
                            </Border>

                            <!-- عدد الأيام -->
                            <Border Grid.Row="1" Grid.Column="2" Background="#FFF8E1" CornerRadius="8" Margin="8" Padding="15">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                        <TextBlock Text="⏱️" FontSize="14" Margin="0,0,8,0"/>
                                        <TextBlock Text="عدد الأيام (آلي)" FontWeight="Bold" FontSize="14"/>
                                    </StackPanel>
                                    <TextBox Text="{Binding DaysCount}" IsReadOnly="True"
                                            Height="40" FontSize="16" Background="White" FontWeight="Bold"
                                            BorderBrush="#FFC107" BorderThickness="2"
                                            HorizontalContentAlignment="Center" VerticalContentAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <!-- أزرار Excel ورقم الزيارة ODK -->
                            <Border Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="5" Background="#E8F5E8" CornerRadius="8" Margin="8" Padding="20">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                        <TextBlock Text="📊" FontSize="16" Margin="0,0,10,0"/>
                                        <TextBlock Text="استيراد Excel ورقم الزيارة ODK" FontWeight="Bold" FontSize="16"/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <!-- أزرار Excel -->
                                        <Button Command="{Binding ImportExcelCommand}" Height="55" Width="200"
                                               Background="#4CAF50" Foreground="White" BorderThickness="0"
                                               FontWeight="Bold" FontSize="14" Margin="0,0,20,0">
                                            <Button.Style>
                                                <Style TargetType="Button">
                                                    <Setter Property="Template">
                                                        <Setter.Value>
                                                            <ControlTemplate TargetType="Button">
                                                                <Border Background="{TemplateBinding Background}"
                                                                       BorderBrush="{TemplateBinding BorderBrush}"
                                                                       BorderThickness="{TemplateBinding BorderThickness}"
                                                                       CornerRadius="10">
                                                                    <Border.Effect>
                                                                        <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="3" Opacity="0.5"/>
                                                                    </Border.Effect>
                                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                                </Border>
                                                            </ControlTemplate>
                                                        </Setter.Value>
                                                    </Setter>
                                                </Style>
                                            </Button.Style>
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="📊" FontSize="20" Margin="0,0,10,0"/>
                                                <TextBlock Text="إدراج ملف Excel" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </Button>

                                        <Button Command="{Binding ShowBatchImportCommand}" Height="55" Width="220"
                                               Background="#FF5722" Foreground="White" BorderThickness="0"
                                               FontWeight="Bold" FontSize="14" Margin="0,0,30,0">
                                            <Button.Style>
                                                <Style TargetType="Button">
                                                    <Setter Property="Template">
                                                        <Setter.Value>
                                                            <ControlTemplate TargetType="Button">
                                                                <Border Background="{TemplateBinding Background}"
                                                                       BorderBrush="{TemplateBinding BorderBrush}"
                                                                       BorderThickness="{TemplateBinding BorderThickness}"
                                                                       CornerRadius="10">
                                                                    <Border.Effect>
                                                                        <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="3" Opacity="0.5"/>
                                                                    </Border.Effect>
                                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                                </Border>
                                                            </ControlTemplate>
                                                        </Setter.Value>
                                                    </Setter>
                                                </Style>
                                            </Button.Style>
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="📂" FontSize="18" Margin="0,0,8,0"/>
                                                <TextBlock Text="استيراد متعدد الملفات" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </Button>



                                    <!-- رقم الزيارة ODK -->
                                        <StackPanel Orientation="Horizontal">
                                            <StackPanel Orientation="Horizontal" Margin="0,0,15,0">
                                                <TextBlock Text="🆔" FontSize="18" Margin="0,0,10,0"/>
                                                <TextBlock Text="رقم الزيارة ODK:" FontWeight="Bold" FontSize="16" VerticalAlignment="Center" Foreground="#E65100"/>
                                            </StackPanel>
                                            <TextBox Text="{Binding OdkVisitNumber}" IsReadOnly="True"
                                                    Width="200" Height="55" FontSize="16" Background="White" FontWeight="Bold"
                                                    BorderBrush="#FF9800" BorderThickness="3"
                                                    HorizontalContentAlignment="Center" VerticalContentAlignment="Center">
                                                <TextBox.Style>
                                                    <Style TargetType="TextBox">
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate TargetType="TextBox">
                                                                    <Border Background="{TemplateBinding Background}"
                                                                           BorderBrush="{TemplateBinding BorderBrush}"
                                                                           BorderThickness="{TemplateBinding BorderThickness}"
                                                                           CornerRadius="10">
                                                                        <ScrollViewer x:Name="PART_ContentHost" Margin="8"/>
                                                                    </Border>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Style>
                                                </TextBox.Style>
                                            </TextBox>
                                        </StackPanel>
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- عقد السائق -->
                            <Border Grid.Row="1" Grid.Column="3" Background="#E8EAF6" CornerRadius="8" Margin="8" Padding="15">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                        <TextBlock Text="🔐" FontSize="14" Margin="0,0,8,0"/>
                                        <TextBlock Text="عقد السائق (آلي)" FontWeight="Bold" FontSize="14"/>
                                    </StackPanel>
                                    <TextBox Text="{Binding DriverContract}" IsReadOnly="True"
                                            Height="40" FontSize="12" Background="White" FontWeight="Bold"
                                            BorderBrush="#3F51B5" BorderThickness="2"
                                            HorizontalContentAlignment="Center" VerticalContentAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <!-- التاريخ الهجري -->
                            <Border Grid.Row="1" Grid.Column="4" Background="#FFF3E0" CornerRadius="8" Margin="8" Padding="15">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                        <TextBlock Text="🌙" FontSize="14" Margin="0,0,8,0"/>
                                        <TextBlock Text="التاريخ الهجري (آلي)" FontWeight="Bold" FontSize="14"/>
                                    </StackPanel>
                                    <TextBox Text="{Binding HijriDate}" IsReadOnly="True"
                                            Height="40" FontSize="14" Background="White" FontWeight="Bold"
                                            BorderBrush="#FF9800" BorderThickness="2"
                                            HorizontalContentAlignment="Center" VerticalContentAlignment="Center"/>
                                </StackPanel>
                            </Border>








                        </Grid>
                    </StackPanel>
                </Border>

                <!-- سؤال الموافقة - يظهر فقط عندما يكون رقم الزيارة فارغ -->
                <Border Background="#FFF3E0" CornerRadius="12" Margin="0,15,0,0" Padding="20"
                       BorderBrush="#FF9800" BorderThickness="2"
                       Visibility="{Binding ShouldShowApprovalQuestion, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <StackPanel>
                        <!-- Header -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <TextBlock Text="⚠️" FontSize="18" Margin="0,0,10,0"/>
                            <TextBlock Text="الموافقة على السفر" FontSize="18" FontWeight="Bold" Foreground="#E65100"/>
                        </StackPanel>

                        <!-- Question -->
                        <TextBlock Text="من قام بالموافقة على هذه الزيارة الميدانية؟"
                                  FontSize="16" FontWeight="SemiBold" Margin="0,0,0,15" Foreground="#BF360C"/>

                        <!-- Options -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button Content="مدير الفرع" Margin="10" Padding="20,10" FontSize="14" FontWeight="Bold"
                                   Background="#4CAF50" Foreground="White" BorderThickness="0"
                                   Command="{Binding SetApprovalCommand}" CommandParameter="branch_manager">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}" CornerRadius="8" Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Button.Style>
                            </Button>
                            <Button Content="نائب مدير الفرع" Margin="10" Padding="20,10" FontSize="14" FontWeight="Bold"
                                   Background="#2196F3" Foreground="White" BorderThickness="0"
                                   Command="{Binding SetApprovalCommand}" CommandParameter="branch_deputy">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}" CornerRadius="8" Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Button.Style>
                            </Button>
                            <Button Content="ضابط المشروع للبرنامج" Margin="10" Padding="20,10" FontSize="14" FontWeight="Bold"
                                   Background="#9C27B0" Foreground="White" BorderThickness="0"
                                   Command="{Binding SetApprovalCommand}" CommandParameter="program_officer">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}" CornerRadius="8" Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Button.Style>
                            </Button>
                        </StackPanel>

                        <!-- Selected Approval Display -->
                        <Border Background="White" CornerRadius="8" Margin="0,15,0,0" Padding="15"
                               BorderBrush="#FF9800" BorderThickness="1"
                               Visibility="{Binding ApprovalBy, Converter={StaticResource StringToVisibilityConverter}}">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <TextBlock Text="✅" FontSize="16" Margin="0,0,10,0"/>
                                <TextBlock Text="تمت الموافقة من: " FontSize="14" FontWeight="Bold"/>
                                <TextBlock Text="{Binding ApprovalBy}" FontSize="14" FontWeight="Bold" Foreground="#E65100"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Border>

                <!-- Professional Mission Purpose Section -->
                <Border CornerRadius="12" Margin="10" Padding="25">
                    <Border.Background>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                            <GradientStop Color="White" Offset="0"/>
                            <GradientStop Color="#F1F8E9" Offset="1"/>
                        </LinearGradientBrush>
                    </Border.Background>
                    <Border.Effect>
                        <DropShadowEffect Color="#4CAF50" Direction="270" ShadowDepth="5" Opacity="0.2" BlurRadius="15"/>
                    </Border.Effect>

                    <StackPanel>
                        <!-- Section Header -->
                        <Border Background="#4CAF50" CornerRadius="8" Padding="15,10" Margin="0,0,0,20">
                            <StackPanel Orientation="Horizontal">
                                <Border Background="White" CornerRadius="15" Width="30" Height="30" Margin="0,0,15,0">
                                    <TextBlock Text="🎯" FontSize="16" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="تفاصيل مهمة النزول الميداني"
                                          FontSize="20" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <Border Background="White" CornerRadius="8" BorderBrush="#4CAF50" BorderThickness="2" Padding="15">
                            <StackPanel>
                                <TextBlock Text="📝 وصف تفصيلي لمهمة النزول:" FontWeight="Bold" FontSize="14"
                                          Foreground="#2E7D32" Margin="0,0,0,10"/>
                                <TextBox Text="{Binding MissionPurpose, UpdateSourceTrigger=PropertyChanged}"
                                        Height="100" FontSize="14" TextWrapping="Wrap"
                                        AcceptsReturn="True" VerticalScrollBarVisibility="Auto"
                                        BorderBrush="#81C784" BorderThickness="1"
                                        Padding="10" Background="#F9FBE7"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Border>

                <!-- Professional Visitors Section -->
                <Border CornerRadius="12" Margin="10" Padding="25">
                    <Border.Background>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                            <GradientStop Color="White" Offset="0"/>
                            <GradientStop Color="#FFF3E0" Offset="1"/>
                        </LinearGradientBrush>
                    </Border.Background>
                    <Border.Effect>
                        <DropShadowEffect Color="#FF9800" Direction="270" ShadowDepth="5" Opacity="0.2" BlurRadius="15"/>
                    </Border.Effect>

                    <StackPanel>
                        <!-- Section Header -->
                        <Border Background="#FF9800" CornerRadius="8" Padding="15,10" Margin="0,0,0,20">
                            <StackPanel Orientation="Horizontal">
                                <Border Background="White" CornerRadius="15" Width="30" Height="30" Margin="0,0,15,0">
                                    <TextBlock Text="👥" FontSize="16" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="القائمين بالزيارة الميدانية"
                                              FontSize="20" FontWeight="Bold" Foreground="White"/>
                                    <TextBlock Text="اختر القطاع وعدد المشاركين أولاً"
                                              FontSize="12" Foreground="#FFF3E0" Margin="0,2,0,0"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>

                        <!-- Compact Three-Column Layout for Visit Team -->
                        <Border Background="White" CornerRadius="6" BorderBrush="#FFB74D" BorderThickness="2" Padding="12" Margin="0,0,0,15">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="180"/>
                                    <ColumnDefinition Width="150"/>
                                </Grid.ColumnDefinitions>

                                <!-- Sector Selection -->
                                <Border Grid.Column="0" Background="#FFF8E1" CornerRadius="6" Margin="0,0,90,0" Padding="8" Grid.ColumnSpan="2">
                                    <StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                            <TextBlock Text="🏢" FontSize="12" Margin="0,0,4,0"/>
                                            <TextBlock Text="اختيار القطاع" FontWeight="Bold" FontSize="12" Foreground="#E65100"/>
                                        </StackPanel>
                                        <ComboBox ItemsSource="{Binding Sectors}"
                                                 SelectedItem="{Binding SelectedSector}"
                                                 Height="32" FontSize="11" BorderBrush="#FF9800" BorderThickness="1">
                                            <ComboBox.ItemTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal" Margin="2">
                                                        <TextBlock Text="🏢" FontSize="9" Margin="0,0,4,0"/>
                                                        <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="11"/>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </ComboBox.ItemTemplate>
                                        </ComboBox>
                                    </StackPanel>
                                </Border>

                                <!-- Visitors Count -->
                                <Border Grid.Column="1" Background="#E8F5E8" CornerRadius="6" Margin="113,0,10,0" Padding="8" Grid.ColumnSpan="2">
                                    <StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                            <TextBlock Text="👥" FontSize="12" Margin="0,0,4,0"/>
                                            <TextBlock Text="عدد المشاركين" FontWeight="Bold" FontSize="12" Foreground="#2E7D32"/>
                                        </StackPanel>
                                        <Grid>
                                            <TextBox Text="{Binding VisitorsCount, UpdateSourceTrigger=PropertyChanged}"
                                                    Height="32" FontSize="12" BorderBrush="#4CAF50" BorderThickness="1"
                                                    HorizontalContentAlignment="Center" VerticalContentAlignment="Center"
                                                    MaxLength="2"/>
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center" Margin="6,0">
                                                <Button Content="➕" Width="22" Height="22" FontSize="10" Background="#4CAF50" Foreground="White"
                                                       BorderThickness="0" Margin="1,0" Command="{Binding IncreaseVisitorsCommand}"/>
                                                <Button Content="➖" Width="22" Height="22" FontSize="10" Background="#F44336" Foreground="White"
                                                       BorderThickness="0" Margin="1,0" Command="{Binding DecreaseVisitorsCommand}"/>
                                            </StackPanel>
                                        </Grid>
                                    </StackPanel>
                                </Border>

                                <!-- تم إزالة زر "إضافة الزيارة" -->
                            </Grid>
                        </Border>

                        <!-- Visitors Grid - 3 Columns Layout -->
                        <ScrollViewer MaxHeight="300" VerticalScrollBarVisibility="Auto">
                            <ItemsControl ItemsSource="{Binding VisitorInputs}">
                                <ItemsControl.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <UniformGrid Columns="3" HorizontalAlignment="Stretch"/>
                                    </ItemsPanelTemplate>
                                </ItemsControl.ItemsPanel>
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <!-- Compact Visitor Card -->
                                        <Border CornerRadius="6" Margin="3" Padding="8" Background="#FFF8E1" BorderBrush="#FFB74D" BorderThickness="1">
                                            <StackPanel>
                                                <!-- Visitor Number -->
                                                <Border Background="#FF9800" CornerRadius="12" Width="24" Height="24" Margin="0,0,0,5" HorizontalAlignment="Center">
                                                    <TextBlock Text="{Binding Number}" FontWeight="Bold" FontSize="10"
                                                              HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="White"/>
                                                </Border>

                                                <!-- Officer Selection -->
                                                <ComboBox ItemsSource="{Binding DataContext.SectorOfficers, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                         SelectedItem="{Binding SelectedOfficer}"
                                                         Height="35" FontSize="13" BorderBrush="#FF9800" BorderThickness="1">
                                                    <ComboBox.ItemTemplate>
                                                        <DataTemplate>
                                                            <Border Background="#FFF8E1" CornerRadius="4" Padding="8,4" Margin="2">
                                                                <StackPanel Orientation="Horizontal">
                                                                    <TextBlock Text="👤" FontSize="13" Margin="0,0,6,0"/>
                                                                    <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="13" Foreground="#E65100" TextTrimming="CharacterEllipsis"/>
                                                                    <TextBlock Text=" - " FontSize="12" Foreground="#666"/>
                                                                    <TextBlock Text="{Binding Rank}" FontSize="12" Foreground="#666" TextTrimming="CharacterEllipsis"/>
                                                                    <TextBlock Text=" (" FontSize="12" Foreground="#666"/>
                                                                    <TextBlock Text="{Binding Code}" FontSize="12" Foreground="#4CAF50" FontWeight="Bold"/>
                                                                    <TextBlock Text=")" FontSize="12" Foreground="#666"/>
                                                                </StackPanel>
                                                            </Border>
                                                        </DataTemplate>
                                                    </ComboBox.ItemTemplate>
                                                </ComboBox>
                                            </StackPanel>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>

                        <!-- Itinerary Section -->
                        <Border Background="#FFF3E0" CornerRadius="12" Padding="20" Margin="0,20,0,0"
                               BorderBrush="#FF9800" BorderThickness="2">
                            <Border.Effect>
                                <DropShadowEffect Color="#FF9800" Direction="270" ShadowDepth="3" Opacity="0.2" BlurRadius="8"/>
                            </Border.Effect>

                            <StackPanel>
                                <!-- Section Header -->
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                    <Border Background="#FF9800" CornerRadius="12" Width="24" Height="24" Margin="0,0,10,0">
                                        <TextBlock Text="🗺️" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                    <TextBlock Text="خط السير للزيارة الميدانية (مستقل)" FontSize="16" FontWeight="Bold"
                                              Foreground="#E65100" VerticalAlignment="Center"/>

                                    <!-- Control Buttons -->
                                    <StackPanel Orientation="Horizontal" Margin="20,0,0,0">
                                        <!-- Decrease Button -->
                                        <Button Content="−" FontSize="14" FontWeight="Bold"
                                               Background="#F44336" Foreground="White" BorderThickness="0"
                                               Width="25" Height="25" Margin="0,0,5,0"
                                               Command="{Binding DecreaseItineraryCommand}">
                                            <Button.Template>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}"
                                                           CornerRadius="12" Width="25" Height="25">
                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Button.Template>
                                        </Button>

                                        <!-- Count Display -->
                                        <Border Background="#FF9800" CornerRadius="8" Padding="8,4" Margin="0,0,5,0">
                                            <TextBlock Text="{Binding ItineraryDays.Count}" FontWeight="Bold" FontSize="12"
                                                      Foreground="White" HorizontalAlignment="Center"/>
                                        </Border>

                                        <!-- Increase Button -->
                                        <Button Content="+" FontSize="14" FontWeight="Bold"
                                               Background="#4CAF50" Foreground="White" BorderThickness="0"
                                               Width="25" Height="25"
                                               Command="{Binding IncreaseItineraryCommand}">
                                            <Button.Template>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}"
                                                           CornerRadius="12" Width="25" Height="25">
                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Button.Template>
                                        </Button>
                                    </StackPanel>
                                </StackPanel>

                                <!-- Itinerary Days -->
                                <ScrollViewer MaxHeight="200" VerticalScrollBarVisibility="Auto">
                                    <ItemsControl ItemsSource="{Binding ItineraryDays}">
                                        <ItemsControl.ItemsPanel>
                                            <ItemsPanelTemplate>
                                                <StackPanel Orientation="Vertical"/>
                                            </ItemsPanelTemplate>
                                        </ItemsControl.ItemsPanel>
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <Border Background="White" CornerRadius="8" Padding="15" Margin="0,0,0,10"
                                                       BorderBrush="#FFB74D" BorderThickness="1">
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="*"/>
                                                        </Grid.ColumnDefinitions>

                                                        <!-- Day Number -->
                                                        <Border Grid.Column="0" Background="#FF9800" CornerRadius="15"
                                                               Width="30" Height="30" Margin="0,0,15,0">
                                                            <TextBlock Text="{Binding DayNumber}" FontWeight="Bold" FontSize="12"
                                                                      HorizontalAlignment="Center" VerticalAlignment="Center"
                                                                      Foreground="White"/>
                                                        </Border>

                                                        <!-- Itinerary Input -->
                                                        <StackPanel Grid.Column="1">
                                                            <TextBlock Text="{Binding DayLabel}" FontWeight="Bold" FontSize="12"
                                                                      Foreground="#E65100" Margin="0,0,0,5"/>
                                                            <TextBox Text="{Binding Itinerary, UpdateSourceTrigger=PropertyChanged}"
                                                                    FontSize="12" Padding="8" BorderBrush="#FFB74D" BorderThickness="1"
                                                                    Background="#FFFEF7" TextWrapping="Wrap" AcceptsReturn="True"
                                                                    MinHeight="35" MaxHeight="80"
                                                                    VerticalScrollBarVisibility="Auto">
                                                                <TextBox.Style>
                                                                    <Style TargetType="TextBox">
                                                                        <Style.Triggers>
                                                                            <Trigger Property="Text" Value="">
                                                                                <Setter Property="Background">
                                                                                    <Setter.Value>
                                                                                        <VisualBrush AlignmentX="Left" AlignmentY="Center" Stretch="None">
                                                                                            <VisualBrush.Visual>
                                                                                                <TextBlock Text="أدخل تفاصيل خط السير لهذا اليوم..."
                                                                                                          FontStyle="Italic" Foreground="#999" Margin="8,0,0,0"/>
                                                                                            </VisualBrush.Visual>
                                                                                        </VisualBrush>
                                                                                    </Setter.Value>
                                                                                </Setter>
                                                                            </Trigger>
                                                                        </Style.Triggers>
                                                                    </Style>
                                                                </TextBox.Style>
                                                            </TextBox>
                                                        </StackPanel>
                                                    </Grid>
                                                </Border>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </ScrollViewer>
                            </StackPanel>
                        </Border>

                        <!-- Projects Section -->
                        <Border Background="#F3E5F5" CornerRadius="12" Padding="20" Margin="0,20,0,0"
                               BorderBrush="#9C27B0" BorderThickness="2">
                            <Border.Effect>
                                <DropShadowEffect Color="#9C27B0" Direction="270" ShadowDepth="3" Opacity="0.2" BlurRadius="8"/>
                            </Border.Effect>

                            <StackPanel>
                                <!-- Section Header -->
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                    <Border Background="#9C27B0" CornerRadius="12" Width="24" Height="24" Margin="0,0,10,0">
                                        <TextBlock Text="📋" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                    <TextBlock Text="المشاريع المنفذة ضمن خطة النزول" FontSize="16" FontWeight="Bold"
                                              Foreground="#6A1B9A" VerticalAlignment="Center"/>
                                </StackPanel>

                                <!-- Projects Count -->
                                <Grid Margin="0,0,0,15">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Grid.Column="0" Text="عدد المشاريع:" FontWeight="Bold" FontSize="14"
                                              Foreground="#6A1B9A" VerticalAlignment="Center" Margin="0,0,10,0"/>

                                    <!-- Decrease Button -->
                                    <Button Grid.Column="1" Content="−" FontSize="16" FontWeight="Bold"
                                           Background="#F44336" Foreground="White" BorderThickness="0"
                                           Width="30" Height="30" Margin="0,0,5,0"
                                           Command="{Binding DecreaseProjectsCommand}">
                                        <Button.Template>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                       CornerRadius="15" Width="30" Height="30">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Button.Template>
                                    </Button>

                                    <!-- Projects Count Display -->
                                    <Border Grid.Column="2" Background="#9C27B0" CornerRadius="8"
                                           Padding="12,6" Margin="0,0,5,0">
                                        <TextBlock Text="{Binding ProjectsCount}" FontWeight="Bold" FontSize="14"
                                                  Foreground="White" HorizontalAlignment="Center"/>
                                    </Border>

                                    <!-- Increase Button -->
                                    <Button Grid.Column="3" Content="+" FontSize="16" FontWeight="Bold"
                                           Background="#4CAF50" Foreground="White" BorderThickness="0"
                                           Width="30" Height="30" Margin="0,0,10,0"
                                           Command="{Binding IncreaseProjectsCommand}">
                                        <Button.Template>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                       CornerRadius="15" Width="30" Height="30">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Button.Template>
                                    </Button>
                                </Grid>

                                <!-- Projects List -->
                                <ScrollViewer MaxHeight="300" VerticalScrollBarVisibility="Auto">
                                    <ItemsControl ItemsSource="{Binding ProjectInputs}">
                                        <ItemsControl.ItemsPanel>
                                            <ItemsPanelTemplate>
                                                <StackPanel Orientation="Vertical"/>
                                            </ItemsPanelTemplate>
                                        </ItemsControl.ItemsPanel>
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <Border Background="White" CornerRadius="8" Padding="15" Margin="0,0,0,10"
                                                       BorderBrush="#CE93D8" BorderThickness="1">
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="120"/>
                                                            <ColumnDefinition Width="80"/>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>

                                                        <!-- Project Number -->
                                                        <Border Grid.Column="0" Background="#9C27B0" CornerRadius="15"
                                                               Width="30" Height="30" Margin="0,0,15,0">
                                                            <TextBlock Text="{Binding Number}" FontWeight="Bold" FontSize="12"
                                                                      HorizontalAlignment="Center" VerticalAlignment="Center"
                                                                      Foreground="White"/>
                                                        </Border>

                                                        <!-- Project Number Input -->
                                                        <StackPanel Grid.Column="1" Margin="0,0,15,0">
                                                            <TextBlock Text="رقم المشروع" FontWeight="Bold" FontSize="12"
                                                                      Foreground="#6A1B9A" Margin="0,0,0,5"/>
                                                            <TextBox Text="{Binding ProjectNumber, UpdateSourceTrigger=PropertyChanged}"
                                                                    FontSize="12" Padding="8" BorderBrush="#CE93D8" BorderThickness="1"
                                                                    Background="#FAFAFA">
                                                                <i:Interaction.Triggers>
                                                                    <i:EventTrigger EventName="TextChanged">
                                                                        <i:InvokeCommandAction Command="{Binding DataContext.SearchProjectCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                                                              CommandParameter="{Binding}"/>
                                                                    </i:EventTrigger>
                                                                </i:Interaction.Triggers>
                                                            </TextBox>
                                                        </StackPanel>

                                                        <!-- Project Days Input -->
                                                        <StackPanel Grid.Column="2" Margin="0,0,15,0">
                                                            <TextBlock Text="عدد الأيام" FontWeight="Bold" FontSize="12"
                                                                      Foreground="#6A1B9A" Margin="0,0,0,5"/>
                                                            <TextBox Text="{Binding ProjectDays, UpdateSourceTrigger=PropertyChanged}"
                                                                    FontSize="12" Padding="8" BorderBrush="#CE93D8" BorderThickness="1"
                                                                    Background="#FAFAFA" Width="80"/>
                                                        </StackPanel>

                                                        <!-- Project Name Display -->
                                                        <StackPanel Grid.Column="3" Margin="0,0,15,0">
                                                            <TextBlock Text="اسم المشروع" FontWeight="Bold" FontSize="12"
                                                                      Foreground="#6A1B9A" Margin="0,0,0,5"/>
                                                            <TextBlock Text="{Binding ProjectName}" FontSize="11"
                                                                      Foreground="{Binding IsProjectFound, Converter={StaticResource BoolToColorConverter}}"
                                                                      TextWrapping="Wrap" MaxHeight="60"
                                                                      Background="#F5F5F5" Padding="8"/>
                                                        </StackPanel>

                                                        <!-- تم إزالة زر "إضافة مشروع" -->
                                                    </Grid>
                                                </Border>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </ScrollViewer>
                            </StackPanel>
                        </Border>



                        <!-- Info Message -->
                        <Border Background="#FFF3E0" CornerRadius="8" Padding="15" Margin="0,15,0,0"
                               BorderBrush="#FF9800" BorderThickness="1">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="💡" FontSize="16" Margin="0,0,10,0"/>
                                <TextBlock Text="ملاحظة: خط السير مستقل تماماً عن عدد الأيام - استخدم الأزرار + و - للتحكم. مجموع أيام المشاريع يجب أن يساوي عدد أيام الزيارة"
                                          FontSize="12" Foreground="#E65100" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>

                        <!-- Itinerary Section -->
                        <Border Background="#E8F5E8" CornerRadius="12" Padding="20" Margin="0,20,0,0"
                               BorderBrush="#4CAF50" BorderThickness="2">
                            <Border.Effect>
                                <DropShadowEffect Color="#4CAF50" Direction="270" ShadowDepth="3" Opacity="0.2" BlurRadius="8"/>
                            </Border.Effect>
                            <StackPanel>
                                <!-- Section Header -->
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                    <Border Background="#4CAF50" CornerRadius="12" Width="24" Height="24" Margin="0,0,10,0">
                                        <TextBlock Text="🗺️" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                    <TextBlock Text="خط السير للنقاط الأمنية" FontSize="16" FontWeight="Bold"
                                              Foreground="#2E7D32" VerticalAlignment="Center"/>
                                </StackPanel>

                                <!-- Security Route Input -->
                                <TextBox Text="{Binding SecurityRoute, UpdateSourceTrigger=PropertyChanged}"
                                        Height="80" FontSize="14" Padding="10"
                                        Background="White" BorderBrush="#4CAF50" BorderThickness="2"
                                        AcceptsReturn="True" TextWrapping="Wrap"
                                        VerticalScrollBarVisibility="Auto"
                                        VerticalContentAlignment="Top">
                                    <TextBox.Style>
                                        <Style TargetType="TextBox">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="TextBox">
                                                        <Border Background="{TemplateBinding Background}"
                                                               BorderBrush="{TemplateBinding BorderBrush}"
                                                               BorderThickness="{TemplateBinding BorderThickness}"
                                                               CornerRadius="8">
                                                            <ScrollViewer x:Name="PART_ContentHost" Margin="5"/>
                                                        </Border>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </TextBox.Style>
                                </TextBox>
                            </StackPanel>
                        </Border>

                        <!-- Notes Section -->
                        <Border Background="#FFF8E1" CornerRadius="12" Padding="20" Margin="0,20,0,0"
                               BorderBrush="#FFC107" BorderThickness="2">
                            <Border.Effect>
                                <DropShadowEffect Color="#FFC107" Direction="270" ShadowDepth="3" Opacity="0.2" BlurRadius="8"/>
                            </Border.Effect>
                            <StackPanel>
                                <!-- Section Header -->
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                    <Border Background="#FFC107" CornerRadius="12" Width="24" Height="24" Margin="0,0,10,0">
                                        <TextBlock Text="📝" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                    <TextBlock Text="ملاحظات إضافية" FontSize="16" FontWeight="Bold"
                                              Foreground="#F57F17" VerticalAlignment="Center"/>
                                </StackPanel>

                                <!-- Visit Notes Input -->
                                <TextBox Text="{Binding VisitNotes, UpdateSourceTrigger=PropertyChanged}"
                                        Height="80" FontSize="14" Padding="10"
                                        Background="White" BorderBrush="#FFC107" BorderThickness="2"
                                        AcceptsReturn="True" TextWrapping="Wrap"
                                        VerticalScrollBarVisibility="Auto"
                                        VerticalContentAlignment="Top">
                                    <TextBox.Style>
                                        <Style TargetType="TextBox">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="TextBox">
                                                        <Border Background="{TemplateBinding Background}"
                                                               BorderBrush="{TemplateBinding BorderBrush}"
                                                               BorderThickness="{TemplateBinding BorderThickness}"
                                                               CornerRadius="8">
                                                            <ScrollViewer x:Name="PART_ContentHost" Margin="5"/>
                                                        </Border>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </TextBox.Style>
                                </TextBox>
                            </StackPanel>
                        </Border>



                    </StackPanel>
                </Border>


            </StackPanel>
        </ScrollViewer>

        <!-- Compact Action Bar -->
        <Border Grid.Row="2" Padding="15,8">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="#37474F" Offset="0"/>
                    <GradientStop Color="#455A64" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="Black" Direction="90" ShadowDepth="2" Opacity="0.2"/>
            </Border.Effect>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Clear Data Button -->
                <Button Grid.Column="0" Height="32" MinWidth="100" FontSize="10" FontWeight="Bold"
                       Command="{Binding ClearAllDataCommand}" VerticalAlignment="Center">
                    <Button.Background>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                            <GradientStop Color="#FF5722" Offset="0"/>
                            <GradientStop Color="#D84315" Offset="1"/>
                        </LinearGradientBrush>
                    </Button.Background>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🗑️" FontSize="10" Margin="0,0,4,0"/>
                        <TextBlock Text="تفريغ البيانات" Foreground="White"/>
                    </StackPanel>
                </Button>

                <!-- Center Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <!-- Add Visit Button -->
                    <Button Height="40" MinWidth="130" Margin="8,0" FontSize="12" FontWeight="Bold"
                           Command="{Binding SaveCommand}">
                        <Button.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#4CAF50" Offset="0"/>
                                <GradientStop Color="#2E7D32" Offset="1"/>
                            </LinearGradientBrush>
                        </Button.Background>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="➕" FontSize="14" Margin="0,0,6,0"/>
                            <TextBlock Text="{Binding SaveButtonText}" Foreground="White"/>
                        </StackPanel>
                    </Button>

                    <!-- Delete Button -->
                    <Button Height="40" MinWidth="100" Margin="8,0" FontSize="12" FontWeight="Bold"
                           Command="{Binding DeleteCommand}">
                        <Button.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#FF9800" Offset="0"/>
                                <GradientStop Color="#F57C00" Offset="1"/>
                            </LinearGradientBrush>
                        </Button.Background>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🗑️" FontSize="14" Margin="0,0,6,0"/>
                            <TextBlock Text="حذف" Foreground="White"/>
                        </StackPanel>
                    </Button>

                    <!-- Diagnose Button -->
                    <Button Height="40" MinWidth="100" Margin="8,0" FontSize="12" FontWeight="Bold"
                           Command="{Binding DiagnoseUpdateCommand}">
                        <Button.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#9C27B0" Offset="0"/>
                                <GradientStop Color="#7B1FA2" Offset="1"/>
                            </LinearGradientBrush>
                        </Button.Background>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔍" FontSize="14" Margin="0,0,6,0"/>
                            <TextBlock Text="تشخيص" Foreground="White"/>
                        </StackPanel>
                    </Button>
                </StackPanel>

                <!-- Status -->
                <StackPanel Grid.Column="2" VerticalAlignment="Center" HorizontalAlignment="Right">
                    <TextBlock Text="{Binding StatusMessage}" FontSize="10" Foreground="#81C784"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
