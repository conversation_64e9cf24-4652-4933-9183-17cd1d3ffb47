using System;
using System.Collections.Generic;
using System.Linq;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;

namespace SFDSystem.Views
{
    /// <summary>
    /// نافذة الرسائل الاحترافية المستقلة
    /// </summary>
    public partial class ProfessionalMessagesWindow : Window
    {
        private readonly DriverManagementSystem.ViewModels.ProfessionalMessagesViewModel _viewModel;

        /// <summary>
        /// إنشاء نافذة رسائل احترافية جديدة
        /// </summary>
        public ProfessionalMessagesWindow()
        {
            InitializeComponent();
            _viewModel = new DriverManagementSystem.ViewModels.ProfessionalMessagesViewModel();
            DataContext = _viewModel;
        }

        /// <summary>
        /// إنشاء نافذة رسائل احترافية مع بيانات زيارة محددة
        /// </summary>
        /// <param name="visit">الزيارة المحددة</param>
        public ProfessionalMessagesWindow(DriverManagementSystem.Models.FieldVisit visit) : this()
        {
            if (visit != null)
            {
                System.Diagnostics.Debug.WriteLine($"🔍 ProfessionalMessagesWindow Constructor:");
                System.Diagnostics.Debug.WriteLine($"🔍 Visit.VisitNumber: {visit.VisitNumber}");
                System.Diagnostics.Debug.WriteLine($"🔍 Visit.DepartureDate: {visit.DepartureDate:dd/MM/yyyy}");
                System.Diagnostics.Debug.WriteLine($"🔍 Visit.ReturnDate: {visit.ReturnDate:dd/MM/yyyy}");
                System.Diagnostics.Debug.WriteLine($"🔍 Visit.DaysCount (from DataGrid): {visit.DaysCount}");

                // حساب المدة الصحيحة
                var correctDuration = (visit.ReturnDate - visit.DepartureDate).Days + 1;
                System.Diagnostics.Debug.WriteLine($"🔍 Calculated correctDuration: {correctDuration} days");

                // تحميل بيانات الزيارة
                _viewModel.LoadVisitData(visit);

                System.Diagnostics.Debug.WriteLine($"🔍 After LoadVisitData - VisitDuration: '{_viewModel.VisitDuration}'");
                System.Diagnostics.Debug.WriteLine($"🔍 After LoadVisitData - VisitDurationDays: {_viewModel.VisitDurationDays}");

                // تحديث معلومات الزيارة في الواجهة مع المدة الصحيحة
                System.Diagnostics.Debug.WriteLine($"🔍 Setting VisitInfo - visit.DaysCount: {visit.DaysCount}");
                System.Diagnostics.Debug.WriteLine($"🔍 Setting VisitInfo - correctDuration: {correctDuration}");
                _viewModel.VisitInfo = $"الزيارة رقم: {visit.VisitNumber} | السائق: {visit.DriverContract} | المدة: {correctDuration} أيام";

                // تحميل العروض المحفوظة من قاعدة البيانات
                _ = LoadSavedOffersAsync();

                System.Diagnostics.Debug.WriteLine($"✅ Professional messages window created for visit: {visit.VisitNumber} with correct duration: {correctDuration} days");
            }
        }

        /// <summary>
        /// تحميل العروض المحفوظة عند فتح النافذة - حل قوي
        /// </summary>
        private async Task LoadSavedOffersAsync()
        {
            try
            {
                if (_viewModel?.SelectedVisit != null)
                {
                    System.Diagnostics.Debug.WriteLine($"🔄 تحميل العروض المحفوظة عند فتح النافذة للزيارة: {_viewModel.VisitNumber}");

                    // استخدام دالة التحديث القوية
                    await RefreshOffersFromDatabase(_viewModel.VisitNumber);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error loading saved offers: {ex.Message}");
            }
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔒 Closing professional messages window");
                this.Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error closing window: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج تغيير فلاتر المركبات
        /// </summary>
        private void VehicleFilter_Changed(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is System.Windows.Controls.CheckBox checkBox)
                {
                    System.Diagnostics.Debug.WriteLine($"🔍 Vehicle filter changed: {checkBox.Name} = {checkBox.IsChecked}");
                }

                // تطبيق الفلاتر مع تأخير قصير للسماح بتحديث البيانات
                System.Windows.Threading.Dispatcher.CurrentDispatcher.BeginInvoke(
                    new Action(() => _viewModel?.ApplyVehicleFilters()),
                    System.Windows.Threading.DispatcherPriority.Background);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error applying vehicle filters: {ex.Message}");
            }
        }

        /// <summary>
        /// حفظ وإغلاق النافذة
        /// </summary>
        private void SaveAndCloseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("💾 Saving and closing professional messages window");
                
                // يمكن إضافة منطق الحفظ هنا إذا لزم الأمر
                
                this.Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error saving and closing window: {ex.Message}");
                MessageBox.Show($"حدث خطأ أثناء الحفظ: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// نسخ محتوى الرسالة إلى الحافظة
        /// </summary>
        private void CopyMessage_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(_viewModel?.MessageContent))
                {
                    System.Windows.Clipboard.SetText(_viewModel.MessageContent);
                    MessageBox.Show("✅ تم نسخ الرسالة إلى الحافظة بنجاح!", "نسخ الرسالة",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                    System.Diagnostics.Debug.WriteLine("📋 Message copied to clipboard");
                }
                else
                {
                    MessageBox.Show("⚠️ لا يوجد محتوى رسالة لنسخه!", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error copying message: {ex.Message}");
                MessageBox.Show($"❌ خطأ في نسخ الرسالة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج اختيار طريقة الإرسال
        /// </summary>
        private void SendOption_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            try
            {
                if (sender is System.Windows.Controls.Border border && border.Tag is string option)
                {
                    System.Diagnostics.Debug.WriteLine($"🎯 Send option selected: {option}");

                    switch (option)
                    {
                        case "SMS":
                            _viewModel.IsSMSSelected = true;
                            _viewModel.IsWhatsAppSelected = false;
                            _viewModel.IsEmailSelected = false;
                            break;
                        case "WhatsApp":
                            _viewModel.IsSMSSelected = false;
                            _viewModel.IsWhatsAppSelected = true;
                            _viewModel.IsEmailSelected = false;
                            break;
                        case "Email":
                            _viewModel.IsSMSSelected = false;
                            _viewModel.IsWhatsAppSelected = false;
                            _viewModel.IsEmailSelected = true;
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error selecting send option: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج تغيير اختيار قالب الرسالة
        /// </summary>
        private void MessageTemplate_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            try
            {
                if (sender is System.Windows.Controls.ComboBox comboBox && comboBox.SelectedItem != null)
                {
                    System.Diagnostics.Debug.WriteLine($"📋 Message template selected: {comboBox.SelectedItem}");

                    // تنفيذ أمر توليد الرسالة تلقائياً
                    _viewModel?.GenerateMessageCommand?.Execute(null);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error selecting message template: {ex.Message}");
            }
        }

        /// <summary>
        /// فتح نافذة عروض الأسعار للسائقين المحددين
        /// </summary>
        private void OffersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على السائقين المحددين
                var selectedDrivers = _viewModel?.GetSelectedDrivers();

                if (selectedDrivers?.Any() != true)
                {
                    MessageBox.Show("⚠️ يرجى تحديد سائق واحد على الأقل لعرض الأسعار", "تحذير",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // الحصول على معلومات الزيارة
                var visitNumber = _viewModel?.VisitNumber ?? "001";
                var daysCount = _viewModel?.SelectedVisit?.DaysCount ?? 3;

                System.Diagnostics.Debug.WriteLine($"🔍 ProfessionalMessagesWindow - OffersButton_Click:");
                System.Diagnostics.Debug.WriteLine($"🔍 VisitNumber: {visitNumber}");
                System.Diagnostics.Debug.WriteLine($"🔍 VisitDurationDays: {daysCount}");
                System.Diagnostics.Debug.WriteLine($"🔍 VisitDuration string: '{_viewModel?.VisitDuration}'");
                System.Diagnostics.Debug.WriteLine($"🏆 Opening offers window for {selectedDrivers.Count} drivers, visit: {visitNumber}");

                // فتح نافذة العروض
                var offersWindow = new DriverManagementSystem.Views.OffersWindow(selectedDrivers, visitNumber, daysCount);
                var result = offersWindow.ShowDialog();

                if (result == true)
                {
                    // الحصول على النتائج
                    var selectedOffers = offersWindow.GetSelectedOffers();
                    var winner = offersWindow.GetWinnerOffer();

                    if (selectedOffers?.Any() == true)
                    {
                        System.Diagnostics.Debug.WriteLine($"🏆 Processing {selectedOffers.Count} offers for DataGrid");

                        // إضافة النتائج إلى الجدول
                        foreach (var offer in selectedOffers)
                        {
                            var status = (winner != null && winner.DriverName == offer.DriverName) ? "🏆 فائز" : "تم التقديم";

                            // توليد نص الرسالة للسائق
                            var messageText = _viewModel?.GenerateDriverMessage(offer.DriverName) ?? "";

                            _viewModel?.AddOfferResult(offer.DriverName, offer.FormattedAmount, status, messageText);
                            System.Diagnostics.Debug.WriteLine($"✅ Added to DataGrid: {offer.DriverName} - {offer.FormattedAmount} - {status}");
                        }

                        // إعادة ترتيب العروض حسب السعر (من الأقل إلى الأعلى)
                        _viewModel?.RefreshOffersOrder();

                        // التأكد من تحديث الواجهة
                        Dispatcher.Invoke(() =>
                        {
                            OffersResultsGrid.Items.Refresh();
                            System.Diagnostics.Debug.WriteLine($"🔄 DataGrid refreshed with sorted offers. Items count: {_viewModel?.OffersResults?.Count ?? 0}");
                        });

                        var offersText = string.Join(" | ", selectedOffers.Select(o => o.ToSaveString()));

                        var message = $"✅ تم حفظ العروض للزيارة {visitNumber} بنجاح!\n\n";
                        message += $"📊 تم إضافة {selectedOffers.Count} عرض إلى جدول النتائج\n\n";
                        message += $"العروض المحفوظة:\n{offersText}\n\n";

                        if (winner != null)
                        {
                            message += $"🏆 السائق الفائز: {winner.DriverName} - {winner.FormattedAmount}\n\n";

                            // حفظ نص الرسالة للسائق الفائز
                            _ = SaveWinnerDriverMessage(visitNumber, winner.DriverName);
                        }

                        message += "يمكنك الآن مشاهدة النتائج في الجدول أسفل الشاشة 👇";

                        MessageBox.Show(message, "نجح حفظ العروض",
                                      MessageBoxButton.OK, MessageBoxImage.Information);

                        System.Diagnostics.Debug.WriteLine($"✅ Offers saved successfully for visit {visitNumber}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error opening offers window: {ex.Message}");
                MessageBox.Show($"❌ خطأ في فتح نافذة العروض:\n\n{ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حفظ نص الرسالة للسائق الفائز
        /// </summary>
        private async Task SaveWinnerDriverMessage(string visitNumber, string winnerDriverName)
        {
            try
            {
                // البحث عن السائق الفائز في القائمة المحددة
                var winnerDriver = _viewModel?.FilteredDrivers?.FirstOrDefault(d => d.Name == winnerDriverName);

                if (winnerDriver != null && _viewModel?.SelectedVisit != null)
                {
                    // توليد نص الرسالة للسائق الفائز
                    var messageText = GenerateWinnerMessage(winnerDriver, _viewModel.SelectedVisit);

                    // حفظ نص الرسالة في قاعدة البيانات
                    var dataService = new DatabaseService();
                    var success = await dataService.SaveWinnerDriverMessageAsync(visitNumber, messageText);

                    if (success)
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ تم حفظ نص الرسالة للسائق الفائز: {winnerDriverName}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ فشل في حفظ نص الرسالة للسائق الفائز: {winnerDriverName}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ نص الرسالة للسائق الفائز: {ex.Message}");
            }
        }

        /// <summary>
        /// توليد نص الرسالة للسائق الفائز
        /// </summary>
        private string GenerateWinnerMessage(DriverManagementSystem.ViewModels.DriverModel driver, FieldVisit visit)
        {
            try
            {
                // بناء نص خط السير
                var itineraryText = "";
                if (visit.Itinerary?.Any() == true)
                {
                    itineraryText = string.Join(" - ", visit.Itinerary);
                }

                // بناء نص القائمين بالزيارة (الأسماء فقط بدون الرتب)
                var visitorsText = "";
                if (visit.Visitors?.Any() == true)
                {
                    visitorsText = string.Join(" و ", visit.Visitors.Select(v => v.OfficerName));
                }

                // حساب تواريخ النزول والعودة
                var startDate = visit.DepartureDate.ToString("dd/MM/yyyy");
                var endDate = visit.ReturnDate.ToString("dd/MM/yyyy");

                var messageText = $@"الأخ/{driver.Name} المحترم،
يرجى تقديم عرض سعركم خلال 24 ساعة وذلك للسفر لمدة ({visit.DaysCount} يوم) مع الأخ: {visitorsText}
في المناطق التالية:
{itineraryText}📅 تاريخ النزول: {startDate}📅 تاريخ العودة: {endDate}...وشكراً
ادارة حركة السائقين بالصندوق الاجتماعي فرع ذمار";

                return messageText;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في توليد نص الرسالة: {ex.Message}");
                return $"الأخ/{driver.Name} المحترم، تم اختياركم للزيارة الميدانية رقم {visit.VisitNumber}";
            }
        }

        /// <summary>
        /// فتح نافذة تعديل عروض الأسعار المحفوظة - تصميم احترافي
        /// </summary>
        private async void EditOffersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تعطيل الزر أثناء المعالجة
                EditOffersButton.IsEnabled = false;
                EditOffersButton.Content = "⏳ جاري التحميل...";

                // التحقق من وجود زيارة محددة
                if (_viewModel?.SelectedVisit == null)
                {
                    ShowProfessionalWarning("تنبيه", "يرجى تحديد زيارة ميدانية أولاً لتعديل عروضها.");
                    return;
                }

                // الحصول على معلومات الزيارة
                var visitNumber = _viewModel.VisitNumber;
                var daysCount = _viewModel.SelectedVisit.DaysCount;

                System.Diagnostics.Debug.WriteLine($"🔍 ProfessionalMessagesWindow - EditOffersButton_Click:");
                System.Diagnostics.Debug.WriteLine($"🔍 VisitNumber: {visitNumber}");
                System.Diagnostics.Debug.WriteLine($"🔍 VisitDurationDays: {daysCount}");
                System.Diagnostics.Debug.WriteLine($"✏️ Opening edit offers window for visit: {visitNumber}");

                // التحقق من وجود عروض معروضة في الجدول
                var hasDisplayedOffers = _viewModel?.OffersResults?.Any() == true;

                if (!hasDisplayedOffers)
                {
                    var confirmResult = ShowProfessionalConfirmation(
                        "لا توجد عروض معروضة",
                        $"لا توجد عروض أسعار معروضة في الجدول للزيارة {visitNumber}.\n\nيرجى إضافة عروض أولاً من خلال زر 'عروض الأسعار'.");

                    return;
                }

                // تحويل البيانات المعروضة إلى DriverOffer objects
                var existingOffers = ConvertOffersResultsToDriverOffers();

                System.Diagnostics.Debug.WriteLine($"🔄 Converting {_viewModel?.OffersResults?.Count ?? 0} existing offers for edit mode");

                // فتح نافذة العروض في وضع التعديل مع البيانات الموجودة
                var editOffersWindow = new DriverManagementSystem.Views.OffersWindow(existingOffers, visitNumber, daysCount, isEditMode: true);
                var result = editOffersWindow.ShowDialog();

                if (result == true)
                {
                    // الحصول على النتائج المحدثة
                    var updatedOffers = editOffersWindow.GetSelectedOffers();
                    var winner = editOffersWindow.GetWinnerOffer();

                    if (updatedOffers?.Any() == true)
                    {
                        System.Diagnostics.Debug.WriteLine($"🏆 Processing {updatedOffers.Count} updated offers for DataGrid");

                        // مسح النتائج القديمة مع تأكيد
                        _viewModel?.ClearOffersResults();

                        // إضافة النتائج المحدثة إلى الجدول
                        foreach (var offer in updatedOffers)
                        {
                            var status = (winner != null && winner.DriverName == offer.DriverName) ? "🏆 فائز" : "تم التقديم";

                            // توليد نص الرسالة للسائق
                            var messageText = _viewModel?.GenerateDriverMessage(offer.DriverName) ?? "";

                            _viewModel?.AddOfferResult(offer.DriverName, offer.FormattedAmount, status, messageText);
                            System.Diagnostics.Debug.WriteLine($"✅ Updated in DataGrid: {offer.DriverName} - {offer.FormattedAmount} - {status}");
                        }

                        // إعادة ترتيب العروض حسب السعر (من الأقل إلى الأعلى)
                        _viewModel?.RefreshOffersOrder();

                        // التأكد من تحديث الواجهة
                        Dispatcher.Invoke(() =>
                        {
                            OffersResultsGrid.Items.Refresh();
                            System.Diagnostics.Debug.WriteLine($"🔄 DataGrid refreshed after edit with sorted offers. Items count: {_viewModel?.OffersResults?.Count ?? 0}");
                        });

                        // إنشاء رسالة نجاح احترافية
                        var offersText = string.Join(" | ", updatedOffers.Select(o => o.ToSaveString()));

                        var successData = new
                        {
                            VisitNumber = visitNumber,
                            UpdatedCount = updatedOffers.Count,
                            OffersText = offersText,
                            Winner = winner,
                            Timestamp = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")
                        };

                        ShowProfessionalSuccess("تم التحديث بنجاح",
                            $"✅ تم تحديث عروض الأسعار للزيارة {successData.VisitNumber} بنجاح!\n\n" +
                            $"📊 عدد العروض المحدثة: {successData.UpdatedCount}\n" +
                            $"⏰ وقت التحديث: {successData.Timestamp}\n\n" +
                            (successData.Winner != null ? $"🏆 السائق الفائز: {successData.Winner.DriverName} - {successData.Winner.FormattedAmount}\n\n" : "") +
                            $"💾 تم حفظ جميع التغييرات في قاعدة البيانات");

                        // حفظ البيانات المحدثة في قاعدة البيانات أيضاً
                        await SaveUpdatedOffersToDatabase(updatedOffers, visitNumber, daysCount);

                        // إعادة تحميل البيانات من قاعدة البيانات لضمان التحديث
                        System.Diagnostics.Debug.WriteLine("🔄 إعادة تحميل البيانات من قاعدة البيانات بعد التعديل...");
                        await RefreshOffersFromDatabase(visitNumber);
                    }
                    else
                    {
                        ShowProfessionalWarning("تنبيه", "لم يتم اختيار أي عروض للحفظ.\nيرجى تحديد عرض واحد على الأقل.");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("🚫 User cancelled the edit operation");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تعديل العروض: {ex.Message}");
                ShowProfessionalError("خطأ في تعديل العروض",
                    $"حدث خطأ غير متوقع أثناء تعديل عروض الأسعار:\n\n" +
                    $"📋 تفاصيل الخطأ: {ex.Message}\n\n" +
                    $"💡 يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.");
            }
            finally
            {
                // إعادة تفعيل الزر وإعادة النص الأصلي
                EditOffersButton.IsEnabled = true;
                RestoreEditButtonContent();
            }
        }

        /// <summary>
        /// تحويل البيانات المعروضة في الجدول إلى DriverOffer objects
        /// </summary>
        private List<DriverManagementSystem.Models.DriverOffer> ConvertOffersResultsToDriverOffers()
        {
            var driverOffers = new List<DriverManagementSystem.Models.DriverOffer>();

            try
            {
                if (_viewModel?.OffersResults?.Any() == true)
                {
                    System.Diagnostics.Debug.WriteLine($"🔄 Converting {_viewModel.OffersResults.Count} OffersResults to DriverOffers");

                    foreach (var offerResult in _viewModel.OffersResults)
                    {
                        // البحث عن السائق في القائمة الأساسية للحصول على بياناته الكاملة
                        var driver = _viewModel.AllDrivers?.FirstOrDefault(d => d.Name == offerResult.DriverName);

                        // استخراج المبلغ من النص (إزالة "ريال" والفواصل)
                        var amountText = offerResult.OfferedPrice?.Replace("ريال", "").Replace(",", "").Trim() ?? "0";
                        decimal.TryParse(amountText, out decimal amount);

                        // تحديد حالة الفوز
                        bool isWinner = offerResult.Status?.Contains("فائز") == true || offerResult.Status?.Contains("🏆") == true;
                        bool isSelected = isWinner || offerResult.Status?.Contains("تم التقديم") == true;

                        var driverOffer = new DriverManagementSystem.Models.DriverOffer
                        {
                            DriverId = 0, // DriverModel لا يحتوي على Id
                            DriverName = offerResult.DriverName ?? "",
                            DriverCode = driver?.DriverCode ?? "",
                            PhoneNumber = driver?.PhoneNumber ?? "غير محدد",
                            VehicleType = driver?.VehicleType ?? "غير محدد",
                            VehicleNumber = "غير محدد", // DriverModel لا يحتوي على VehicleNumber
                            DaysCount = _viewModel?.SelectedVisit?.DaysCount ?? 3,
                            ProposedAmount = amount,
                            IsSelected = isSelected,
                            IsWinner = isWinner,
                            OfferStatus = offerResult.Status ?? "تم التقديم"
                        };

                        driverOffers.Add(driverOffer);

                        System.Diagnostics.Debug.WriteLine($"✅ Converted: {offerResult.DriverName} - {amount} ريال - {offerResult.Status}");
                    }

                    System.Diagnostics.Debug.WriteLine($"🎯 Successfully converted {driverOffers.Count} offers for edit mode");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ No OffersResults found to convert");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error converting OffersResults to DriverOffers: {ex.Message}");
            }

            return driverOffers;
        }



        /// <summary>
        /// حفظ العروض المحدثة في قاعدة البيانات مع حفظ حالة الفوز بشكل صحيح
        /// </summary>
        private async Task SaveUpdatedOffersToDatabase(ObservableCollection<DriverManagementSystem.Models.DriverOffer> updatedOffers, string visitNumber, int daysCount)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"💾 حفظ {updatedOffers.Count} عرض محدث في قاعدة البيانات للزيارة: {visitNumber}");

                // طباعة تفاصيل العروض قبل الحفظ للتأكد من الحالات
                foreach (var offer in updatedOffers)
                {
                    System.Diagnostics.Debug.WriteLine($"🔍 عرض للحفظ: {offer.DriverName} - فائز: {offer.IsWinner} - محدد: {offer.IsSelected}");
                }

                using var context = new DriverManagementSystem.Data.ApplicationDbContext();
                var offersService = new DriverManagementSystem.Services.OffersService(context);

                // تحويل ObservableCollection إلى List
                var offersList = updatedOffers.ToList();

                // حفظ العروض المحدثة مع الحالات الصحيحة
                var success = await offersService.SaveVisitOffersAsync(offersList, visitNumber, daysCount);

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم حفظ العروض المحدثة بنجاح في قاعدة البيانات مع الحالات الصحيحة");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ فشل في حفظ العروض المحدثة في قاعدة البيانات");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ العروض المحدثة: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل العروض المحفوظة من قاعدة البيانات تلقائياً عند تحديد زيارة
        /// </summary>
        private async Task LoadSavedOffersFromDatabase(string visitNumber)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 تحميل العروض المحفوظة من قاعدة البيانات للزيارة: {visitNumber}");

                using var context = new DriverManagementSystem.Data.ApplicationDbContext();
                var offersService = new DriverManagementSystem.Services.OffersService(context);

                // جلب العروض المحفوظة
                var savedOffers = await offersService.GetVisitOffersByNumberAsync(visitNumber);

                if (savedOffers?.Any() == true)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم العثور على {savedOffers.Count} عرض محفوظ");

                    // مسح النتائج الحالية
                    _viewModel?.ClearOffersResults();

                    // إضافة العروض المحفوظة إلى الجدول
                    foreach (var offer in savedOffers.OrderBy(o => o.ProposedAmount))
                    {
                        var status = offer.IsWinner ? "🏆 فائز" : "تم التقديم";
                        var messageText = _viewModel?.GenerateDriverMessage(offer.DriverName) ?? "";

                        _viewModel?.AddOfferResult(offer.DriverName, offer.FormattedAmount, status, messageText);
                        System.Diagnostics.Debug.WriteLine($"✅ تم تحميل عرض محفوظ: {offer.DriverName} - {offer.FormattedAmount} - {status}");
                    }

                    // تحديث الواجهة
                    Dispatcher.Invoke(() =>
                    {
                        OffersResultsGrid.Items.Refresh();
                        System.Diagnostics.Debug.WriteLine($"🔄 تم تحديث الجدول بـ {savedOffers.Count} عرض محفوظ");
                    });
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ لا توجد عروض محفوظة للزيارة {visitNumber}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل العروض المحفوظة: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل البيانات تلقائياً عند تحديد زيارة جديدة
        /// </summary>
        public async Task LoadDataForVisit(string visitNumber)
        {
            try
            {
                if (string.IsNullOrEmpty(visitNumber)) return;

                System.Diagnostics.Debug.WriteLine($"🔄 تحميل البيانات للزيارة: {visitNumber}");

                // تحميل العروض المحفوظة
                await LoadSavedOffersFromDatabase(visitNumber);

                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل البيانات للزيارة {visitNumber}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل البيانات للزيارة: {ex.Message}");
            }
        }

        /// <summary>
        /// إعادة تحميل العروض من قاعدة البيانات بعد التعديل - حل قوي
        /// </summary>
        private async Task RefreshOffersFromDatabase(string visitNumber)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 إعادة تحميل العروض من قاعدة البيانات بعد التعديل للزيارة: {visitNumber}");

                using var context = new DriverManagementSystem.Data.ApplicationDbContext();
                var offersService = new DriverManagementSystem.Services.OffersService(context);

                // جلب العروض المحدثة من قاعدة البيانات
                var updatedOffers = await offersService.GetVisitOffersByNumberAsync(visitNumber);

                if (updatedOffers?.Any() == true)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم جلب {updatedOffers.Count} عرض محدث من قاعدة البيانات");

                    // مسح البيانات القديمة تماماً
                    _viewModel?.ClearOffersResults();

                    // إضافة البيانات المحدثة من قاعدة البيانات
                    foreach (var offer in updatedOffers.OrderBy(o => o.ProposedAmount))
                    {
                        var status = offer.IsWinner ? "🏆 فائز" : "تم التقديم";
                        var messageText = _viewModel?.GenerateDriverMessage(offer.DriverName) ?? "";

                        _viewModel?.AddOfferResult(offer.DriverName, offer.FormattedAmount, status, messageText);
                        System.Diagnostics.Debug.WriteLine($"✅ تم إضافة عرض محدث: {offer.DriverName} - {offer.FormattedAmount} - {status}");
                    }

                    // تحديث الواجهة بقوة
                    Dispatcher.Invoke(() =>
                    {
                        OffersResultsGrid.Items.Refresh();
                        OffersResultsGrid.UpdateLayout();
                        System.Diagnostics.Debug.WriteLine($"🔄 تم تحديث الجدول بقوة مع {updatedOffers.Count} عرض محدث من قاعدة البيانات");
                    });

                    System.Diagnostics.Debug.WriteLine("✅ تم إعادة تحميل العروض بنجاح من قاعدة البيانات");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ لا توجد عروض في قاعدة البيانات للزيارة {visitNumber}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعادة تحميل العروض من قاعدة البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من وجود عروض محفوظة للزيارة
        /// </summary>
        private async Task<bool> CheckForExistingOffers(string visitNumber)
        {
            try
            {
                using var context = new DriverManagementSystem.Data.ApplicationDbContext();
                var offersService = new DriverManagementSystem.Services.OffersService(context);
                var existingOffers = await offersService.GetVisitOffersByNumberAsync(visitNumber);
                return existingOffers?.Any() == true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التحقق من العروض الموجودة: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إعادة محتوى زر التعديل إلى حالته الأصلية
        /// </summary>
        private void RestoreEditButtonContent()
        {
            var originalContent = new StackPanel { Orientation = Orientation.Horizontal };

            var iconBorder = new Border
            {
                Background = new SolidColorBrush(Colors.White),
                CornerRadius = new CornerRadius(12),
                Width = 24,
                Height = 24,
                Margin = new Thickness(0, 0, 8, 0)
            };

            var iconText = new TextBlock
            {
                Text = "✏️",
                FontSize = 12,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            iconBorder.Child = iconText;

            var textPanel = new StackPanel { Orientation = Orientation.Vertical };

            var mainText = new TextBlock
            {
                Text = "تعديل العروض",
                FontSize = 11,
                FontWeight = FontWeights.Bold
            };

            var subText = new TextBlock
            {
                Text = "تحرير البيانات المحفوظة",
                FontSize = 8,
                Opacity = 0.8
            };

            textPanel.Children.Add(mainText);
            textPanel.Children.Add(subText);

            originalContent.Children.Add(iconBorder);
            originalContent.Children.Add(textPanel);

            EditOffersButton.Content = originalContent;
        }

        #region Professional Message Dialogs

        /// <summary>
        /// عرض رسالة نجاح احترافية
        /// </summary>
        private void ShowProfessionalSuccess(string title, string message)
        {
            MessageBox.Show(message, $"✅ {title}", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// عرض رسالة تحذير احترافية
        /// </summary>
        private void ShowProfessionalWarning(string title, string message)
        {
            MessageBox.Show(message, $"⚠️ {title}", MessageBoxButton.OK, MessageBoxImage.Warning);
        }

        /// <summary>
        /// عرض رسالة خطأ احترافية
        /// </summary>
        private void ShowProfessionalError(string title, string message)
        {
            MessageBox.Show(message, $"❌ {title}", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// عرض رسالة تأكيد احترافية
        /// </summary>
        private MessageBoxResult ShowProfessionalConfirmation(string title, string message)
        {
            return MessageBox.Show(message, $"❓ {title}", MessageBoxButton.YesNo, MessageBoxImage.Question);
        }

        #endregion

        /// <summary>
        /// تبديل حالة الفوز للسائق - حل قوي لحفظ الحالة
        /// </summary>
        private async void ToggleWinnerStatus_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (sender is TextBlock textBlock && textBlock.Tag is DriverManagementSystem.ViewModels.OfferResult offerResult)
                {
                    System.Diagnostics.Debug.WriteLine($"🔄 تبديل حالة الفوز للسائق: {offerResult.DriverName}");

                    // تبديل الحالة
                    bool newWinnerStatus = offerResult.Status != "🏆 فائز";

                    // إذا كان سيصبح فائز، إلغاء فوز الآخرين أولاً
                    if (newWinnerStatus && _viewModel?.OffersResults != null)
                    {
                        foreach (var offer in _viewModel.OffersResults)
                        {
                            if (offer != offerResult)
                            {
                                offer.Status = "تم التقديم";
                            }
                        }
                    }

                    // تحديث حالة السائق المحدد
                    offerResult.Status = newWinnerStatus ? "🏆 فائز" : "تم التقديم";

                    // تحديث الواجهة
                    Dispatcher.Invoke(() =>
                    {
                        OffersResultsGrid.Items.Refresh();
                    });

                    // حفظ التغييرات في قاعدة البيانات فوراً
                    if (_viewModel?.SelectedVisit != null)
                    {
                        var visitNumber = _viewModel.SelectedVisit.VisitNumber;
                        var daysCount = _viewModel.SelectedVisit.DaysCount;

                        // تحويل النتائج إلى DriverOffer وحفظها فوراً
                        var updatedOffers = new ObservableCollection<DriverManagementSystem.Models.DriverOffer>();
                        foreach (var result in _viewModel.OffersResults)
                        {
                            // استخراج المبلغ من النص
                            var amountText = result.OfferedPrice?.Replace("ريال", "").Replace(",", "").Trim() ?? "0";
                            decimal.TryParse(amountText, out decimal amount);

                            var driverOffer = new DriverManagementSystem.Models.DriverOffer
                            {
                                DriverName = result.DriverName,
                                ProposedAmount = amount,
                                IsWinner = result.Status == "🏆 فائز",
                                IsSelected = true, // جميع العروض المعروضة محددة
                                DaysCount = daysCount
                            };
                            updatedOffers.Add(driverOffer);
                        }

                        // حفظ في قاعدة البيانات فوراً
                        await SaveUpdatedOffersToDatabase(updatedOffers, visitNumber, daysCount);

                        System.Diagnostics.Debug.WriteLine($"✅ تم حفظ تغيير حالة الفوز للسائق: {offerResult.DriverName} - فائز: {newWinnerStatus}");

                        // رسالة تأكيد بسيطة
                        var statusText = newWinnerStatus ? "فائز" : "مرشح";
                        System.Diagnostics.Debug.WriteLine($"🔄 تم تحديث حالة السائق: {offerResult.DriverName} إلى {statusText}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تبديل حالة الفوز: {ex.Message}");
                MessageBox.Show($"❌ خطأ في تحديث الحالة:\n\n{ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حفظ نتائج العروض في سجل الزيارات الميدانية
        /// </summary>
        private async void SaveOffersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من وجود عروض للحفظ
                if (_viewModel?.OffersResults?.Any() != true)
                {
                    MessageBox.Show("لا توجد عروض أسعار للحفظ\n\nيرجى إضافة عروض أولاً من خلال زر 'عروض الأسعار'", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // التحقق من وجود زيارة محددة
                if (_viewModel?.SelectedVisit == null)
                {
                    MessageBox.Show("لا توجد زيارة ميدانية محددة للحفظ\n\nيرجى تحديد زيارة ميدانية أولاً", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show($"هل تريد حفظ عروض الأسعار ({_viewModel.OffersResults.Count} عرض) في قاعدة البيانات؟\n\nالزيارة: {_viewModel.SelectedVisit.VisitNumber}",
                                           "تأكيد الحفظ", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // تعطيل الزر أثناء الحفظ
                    SaveOffersButton.IsEnabled = false;
                    SaveOffersButton.Content = "⏳ جاري الحفظ...";

                    // حفظ الحالة الحقيقية الحالية للعروض
                    var databaseSuccess = await SaveCurrentOffersState();

                    // حفظ البيانات في سجل الزيارات الميدانية أيضاً
                    var fieldVisitSuccess = await _viewModel.SaveSelectedDriversToFieldVisit();

                    if (databaseSuccess && fieldVisitSuccess)
                    {
                        var successMessage = $"✅ تم حفظ العروض بنجاح!\n\n" +
                                           $"📊 عدد العروض المحفوظة: {_viewModel.OffersResults.Count}\n" +
                                           $"🏢 الزيارة الميدانية: {_viewModel.SelectedVisit.VisitNumber}\n" +
                                           $"💾 تم الحفظ في قاعدة البيانات وسجل الزيارات\n" +
                                           $"⏰ وقت الحفظ: {DateTime.Now:yyyy/MM/dd HH:mm:ss}";

                        MessageBox.Show(successMessage, "نجح الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);

                        System.Diagnostics.Debug.WriteLine($"✅ Successfully saved {_viewModel.OffersResults.Count} offers for visit {_viewModel.SelectedVisit.VisitNumber}");
                    }
                    else if (databaseSuccess)
                    {
                        MessageBox.Show($"✅ تم حفظ العروض في قاعدة البيانات بنجاح\n⚠️ لكن فشل في تحديث سجل الزيارات", "حفظ جزئي",
                                      MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                    else if (fieldVisitSuccess)
                    {
                        MessageBox.Show($"✅ تم حفظ البيانات في سجل الزيارات\n⚠️ لكن فشل في حفظ قاعدة البيانات", "حفظ جزئي",
                                      MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                    else
                    {
                        MessageBox.Show("❌ فشل في حفظ البيانات\n\nيرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني", "خطأ في الحفظ",
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error in SaveOffersButton_Click: {ex.Message}");
                MessageBox.Show($"❌ خطأ غير متوقع في حفظ البيانات:\n\n{ex.Message}\n\nيرجى المحاولة مرة أخرى", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة تفعيل الزر
                SaveOffersButton.IsEnabled = true;

                // إعادة المحتوى الأصلي للزر
                var stackPanel = new StackPanel { Orientation = Orientation.Horizontal };
                stackPanel.Children.Add(new TextBlock { Text = "💾", FontSize = 14, Margin = new Thickness(0, 0, 6, 0) });
                stackPanel.Children.Add(new TextBlock { Text = "حفظ", FontSize = 12 });
                SaveOffersButton.Content = stackPanel;
            }
        }

        /// <summary>
        /// حفظ الحالة الحقيقية الحالية للعروض بدون إعادة تحميل
        /// </summary>
        private async Task<bool> SaveCurrentOffersState()
        {
            try
            {
                if (_viewModel?.OffersResults?.Any() != true || _viewModel?.SelectedVisit == null)
                {
                    return false;
                }

                var visitNumber = _viewModel.SelectedVisit.VisitNumber;
                var daysCount = _viewModel.SelectedVisit.DaysCount;

                System.Diagnostics.Debug.WriteLine($"💾 حفظ الحالة الحقيقية للعروض للزيارة: {visitNumber}");

                // تحويل العروض الحالية إلى DriverOffer مع الحالات الصحيحة
                var currentOffers = new List<DriverManagementSystem.Models.DriverOffer>();

                foreach (var offerResult in _viewModel.OffersResults)
                {
                    // استخراج المبلغ من النص
                    var amountText = offerResult.OfferedPrice?.Replace("ريال", "").Replace(",", "").Trim() ?? "0";
                    decimal.TryParse(amountText, out decimal amount);

                    // تحديد حالة الفوز من النص الحالي
                    bool isWinner = offerResult.Status?.Contains("فائز") == true || offerResult.Status?.Contains("🏆") == true;

                    var driverOffer = new DriverManagementSystem.Models.DriverOffer
                    {
                        VisitNumber = visitNumber,
                        DriverName = offerResult.DriverName ?? "",
                        DaysCount = daysCount,
                        ProposedAmount = amount,
                        IsSelected = true,
                        IsWinner = isWinner,
                        OfferStatus = offerResult.Status ?? "تم التقديم",
                        CreatedAt = DateTime.Now
                    };

                    currentOffers.Add(driverOffer);
                    System.Diagnostics.Debug.WriteLine($"🔄 تحضير عرض للحفظ: {offerResult.DriverName} - فائز: {isWinner} - حالة: {offerResult.Status}");
                }

                // حفظ العروض في قاعدة البيانات مع الحالات الصحيحة
                using var context = new DriverManagementSystem.Data.ApplicationDbContext();
                var offersService = new DriverManagementSystem.Services.OffersService(context);

                // حذف العروض القديمة أولاً
                await offersService.DeleteVisitOffersAsync(visitNumber);

                // حفظ العروض الجديدة مع الحالات الصحيحة
                var success = await offersService.SaveVisitOffersAsync(currentOffers, visitNumber, daysCount);

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم حفظ الحالة الحقيقية للعروض بنجاح - {currentOffers.Count} عرض");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ فشل في حفظ الحالة الحقيقية للعروض");
                }

                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ الحالة الحقيقية للعروض: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// مسح نتائج العروض
        /// </summary>
        private void ClearOffersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show("هل تريد مسح جميع نتائج العروض؟", "تأكيد المسح",
                                           MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    _viewModel?.ClearOffersResults();

                    // تحديث الواجهة
                    Dispatcher.Invoke(() =>
                    {
                        OffersResultsGrid.Items.Refresh();
                    });

                    MessageBox.Show("✅ تم مسح جميع نتائج العروض بنجاح", "تم المسح",
                                  MessageBoxButton.OK, MessageBoxImage.Information);

                    System.Diagnostics.Debug.WriteLine("🗑️ Offers results cleared successfully");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error clearing offers results: {ex.Message}");
                MessageBox.Show($"❌ خطأ في مسح النتائج:\n\n{ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// التعامل مع إغلاق النافذة
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔒 Professional messages window closed");

                // تنظيف الموارد إذا لزم الأمر
                _viewModel?.Dispose();

                base.OnClosed(e);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error during window cleanup: {ex.Message}");
                base.OnClosed(e);
            }
        }
    }
}
