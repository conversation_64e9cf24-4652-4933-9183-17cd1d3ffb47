﻿using System;
using System.Configuration;
using System.Data;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Services;
using DriverManagementSystem.Data;
using DriverManagementSystem.Migrations;

namespace DriverManagementSystem;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private async void Application_Startup(object sender, StartupEventArgs e)
    {
        try
        {
            // التحقق من وجود معامل لإضافة البيانات
            if (e.Args.Length > 0 && e.Args[0] == "--add-drivers")
            {
                await AddDriversData();
                Shutdown();
                return;
            }

            // فحص قاعدة البيانات والمستخدمين
            await CheckDatabaseAndUsers();

            // تنظيف رسائل السائق من مهام النزول
            await CleanDriverMessagesOnStartup();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في بدء تشغيل النظام: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.ToString()}",
                "خطأ في النظام", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
        }
    }

    private async Task CheckDatabaseAndUsers()
    {
        try
        {
            // نقل البيانات من SQLite إلى SQL Server إذا لزم الأمر
            await MigrateDataIfNeeded();

            using var context = new ApplicationDbContext();

            // التأكد من إنشاء قاعدة البيانات
            await context.Database.EnsureCreatedAsync();

            // إعادة إنشاء جدول FieldVisitItineraries (الحل النهائي)
            await RecreateFieldVisitItinerariesTable.ApplyAsync(context);

            // فحص وجود مستخدمين
            var usersCount = await context.Users.CountAsync();

            if (usersCount == 0)
            {
                // لا يوجد مستخدمين - إظهار نموذج الإعداد الأولي
                var setupWindow = new Views.InitialSetupWindow();
                var result = setupWindow.ShowDialog();

                if (result != true)
                {
                    // المستخدم ألغى الإعداد - إغلاق النظام
                    Shutdown();
                    return;
                }

                // إضافة البيانات الأساسية بعد إنشاء المستخدم الأول
                var seeder = new InitialDataSeeder(context);
                await seeder.SeedBasicDataAsync();

                // إدراج بيانات السائقين الأولية
                await Data.SeedDriversData.SeedDriversAsync(context);
            }
            else
            {
                // حتى لو كان هناك مستخدمين، تأكد من إدراج بيانات السائقين
                await Data.SeedDriversData.SeedDriversAsync(context);
            }

            // إظهار نافذة تسجيل الدخول
            var loginWindow = new Views.LoginWindow();
            loginWindow.Show();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فحص قاعدة البيانات: {ex.Message}",
                "خطأ في النظام", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
        }
    }

    private async Task AddDriversData()
    {
        try
        {
            Console.WriteLine("🔄 بدء إضافة بيانات السائقين والمركبات والمشاريع...");

            var seeder = new Services.DatabaseSeeder();
            await seeder.SeedAllDataAsync();

            Console.WriteLine("✅ تم إضافة جميع البيانات بنجاح!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ خطأ في إضافة البيانات: {ex.Message}");
        }
    }

    /// <summary>
    /// نقل البيانات من SQLite إلى SQL Server إذا لزم الأمر
    /// </summary>
    private async Task MigrateDataIfNeeded()
    {
        try
        {
            // التحقق من وجود ملف SQLite
            var sqliteDbPath = Path.Combine(Directory.GetCurrentDirectory(), "Data", "SFDSYS.db");
            if (!File.Exists(sqliteDbPath))
            {
                System.Diagnostics.Debug.WriteLine("ℹ️ لا يوجد ملف SQLite للنقل");
                return;
            }

            System.Diagnostics.Debug.WriteLine("🔄 بدء عملية نقل البيانات من SQLite إلى SQL Server...");

            var migrationService = new Services.DataMigrationService();
            var success = await migrationService.MigrateFromSqliteToSqlServerAsync();

            if (success)
            {
                System.Diagnostics.Debug.WriteLine("✅ تم نقل البيانات بنجاح");

                // إنشاء نسخة احتياطية من ملف SQLite
                var backupPath = sqliteDbPath + ".backup_" + DateTime.Now.ToString("yyyyMMdd_HHmmss");
                File.Copy(sqliteDbPath, backupPath);
                System.Diagnostics.Debug.WriteLine($"📁 تم إنشاء نسخة احتياطية: {backupPath}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("❌ فشل في نقل البيانات");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في نقل البيانات: {ex.Message}");
        }
    }

    /// <summary>
    /// تنظيف رسائل السائق من مهام النزول عند بدء التطبيق
    /// </summary>
    private async Task CleanDriverMessagesOnStartup()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🧹 بدء تنظيف رسائل السائق من مهام النزول...");

            var dataService = new DatabaseService();
            var result = await dataService.CleanAllDriverMessagesAsync();

            if (result)
            {
                System.Diagnostics.Debug.WriteLine("✅ تم تنظيف رسائل السائق بنجاح");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على رسائل سائق للتنظيف");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في تنظيف رسائل السائق: {ex.Message}");
            // لا نوقف التطبيق بسبب هذا الخطأ
        }
    }

}

